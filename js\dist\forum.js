(function(We,q,qe){"use strict";function Ee(i){return i!==null&&typeof i=="object"&&"constructor"in i&&i.constructor===Object}function le(i,e){i===void 0&&(i={}),e===void 0&&(e={});const t=["__proto__","constructor","prototype"];Object.keys(e).filter(s=>t.indexOf(s)<0).forEach(s=>{typeof i[s]>"u"?i[s]=e[s]:Ee(e[s])&&Ee(i[s])&&Object.keys(e[s]).length>0&&le(i[s],e[s])})}const be={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function F(){const i=typeof document<"u"?document:{};return le(i,be),i}const Xe={document:be,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(i){return typeof setTimeout>"u"?(i(),null):setTimeout(i,0)},cancelAnimationFrame(i){typeof setTimeout>"u"||clearTimeout(i)}};function R(){const i=typeof window<"u"?window:{};return le(i,Xe),i}function je(i){return i===void 0&&(i=""),i.trim().split(" ").filter(e=>!!e.trim())}function Ye(i){const e=i;Object.keys(e).forEach(t=>{try{e[t]=null}catch{}try{delete e[t]}catch{}})}function xe(i,e){return e===void 0&&(e=0),setTimeout(i,e)}function J(){return Date.now()}function Ue(i){const e=R();let t;return e.getComputedStyle&&(t=e.getComputedStyle(i,null)),!t&&i.currentStyle&&(t=i.currentStyle),t||(t=i.style),t}function Ke(i,e){e===void 0&&(e="x");const t=R();let s,n,r;const l=Ue(i);return t.WebKitCSSMatrix?(n=l.transform||l.webkitTransform,n.split(",").length>6&&(n=n.split(", ").map(a=>a.replace(",",".")).join(", ")),r=new t.WebKitCSSMatrix(n==="none"?"":n)):(r=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=r.toString().split(",")),e==="x"&&(t.WebKitCSSMatrix?n=r.m41:s.length===16?n=parseFloat(s[12]):n=parseFloat(s[4])),e==="y"&&(t.WebKitCSSMatrix?n=r.m42:s.length===16?n=parseFloat(s[13]):n=parseFloat(s[5])),n||0}function ee(i){return typeof i=="object"&&i!==null&&i.constructor&&Object.prototype.toString.call(i).slice(8,-1)==="Object"}function Qe(i){return typeof window<"u"&&typeof window.HTMLElement<"u"?i instanceof HTMLElement:i&&(i.nodeType===1||i.nodeType===11)}function $(){const i=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let t=1;t<arguments.length;t+=1){const s=t<0||arguments.length<=t?void 0:arguments[t];if(s!=null&&!Qe(s)){const n=Object.keys(Object(s)).filter(r=>e.indexOf(r)<0);for(let r=0,l=n.length;r<l;r+=1){const a=n[r],d=Object.getOwnPropertyDescriptor(s,a);d!==void 0&&d.enumerable&&(ee(i[a])&&ee(s[a])?s[a].__swiper__?i[a]=s[a]:$(i[a],s[a]):!ee(i[a])&&ee(s[a])?(i[a]={},s[a].__swiper__?i[a]=s[a]:$(i[a],s[a])):i[a]=s[a])}}}return i}function te(i,e,t){i.style.setProperty(e,t)}function Ie(i){let{swiper:e,targetPosition:t,side:s}=i;const n=R(),r=-e.translate;let l=null,a;const d=e.params.speed;e.wrapperEl.style.scrollSnapType="none",n.cancelAnimationFrame(e.cssModeFrameID);const o=t>r?"next":"prev",f=(h,g)=>o==="next"&&h>=g||o==="prev"&&h<=g,p=()=>{a=new Date().getTime(),l===null&&(l=a);const h=Math.max(Math.min((a-l)/d,1),0),g=.5-Math.cos(h*Math.PI)/2;let m=r+g*(t-r);if(f(m,t)&&(m=t),e.wrapperEl.scrollTo({[s]:m}),f(m,t)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[s]:m})}),n.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=n.requestAnimationFrame(p)};p()}function oe(i){return i.querySelector(".swiper-slide-transform")||i.shadowRoot&&i.shadowRoot.querySelector(".swiper-slide-transform")||i}function H(i,e){e===void 0&&(e="");const t=R(),s=[...i.children];return t.HTMLSlotElement&&i instanceof HTMLSlotElement&&s.push(...i.assignedElements()),e?s.filter(n=>n.matches(e)):s}function Ze(i,e){const t=[e];for(;t.length>0;){const s=t.shift();if(i===s)return!0;t.push(...s.children,...s.shadowRoot?s.shadowRoot.children:[],...s.assignedElements?s.assignedElements():[])}}function Je(i,e){const t=R();let s=e.contains(i);return!s&&t.HTMLSlotElement&&e instanceof HTMLSlotElement&&(s=[...e.assignedElements()].includes(i),s||(s=Ze(i,e))),s}function ie(i){try{console.warn(i);return}catch{}}function Q(i,e){e===void 0&&(e=[]);const t=document.createElement(i);return t.classList.add(...Array.isArray(e)?e:je(e)),t}function et(i,e){const t=[];for(;i.previousElementSibling;){const s=i.previousElementSibling;e?s.matches(e)&&t.push(s):t.push(s),i=s}return t}function tt(i,e){const t=[];for(;i.nextElementSibling;){const s=i.nextElementSibling;e?s.matches(e)&&t.push(s):t.push(s),i=s}return t}function W(i,e){return R().getComputedStyle(i,null).getPropertyValue(e)}function se(i){let e=i,t;if(e){for(t=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(t+=1);return t}}function Ce(i,e){const t=[];let s=i.parentElement;for(;s;)e?s.matches(e)&&t.push(s):t.push(s),s=s.parentElement;return t}function de(i,e,t){const s=R();return i[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(i,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(i,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom"))}function G(i){return(Array.isArray(i)?i:[i]).filter(e=>!!e)}function it(i){return e=>Math.abs(e)>0&&i.browser&&i.browser.need3dFix&&Math.abs(e)%90===0?e+.001:e}function Me(i,e){e===void 0&&(e=""),typeof trustedTypes<"u"?i.innerHTML=trustedTypes.createPolicy("html",{createHTML:t=>t}).createHTML(e):i.innerHTML=e}let ce;function st(){const i=R(),e=F();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in i||i.DocumentTouch&&e instanceof i.DocumentTouch)}}function Pe(){return ce||(ce=st()),ce}let fe;function rt(i){let{userAgent:e}=i===void 0?{}:i;const t=Pe(),s=R(),n=s.navigator.platform,r=e||s.navigator.userAgent,l={ios:!1,android:!1},a=s.screen.width,d=s.screen.height,o=r.match(/(Android);?[\s\/]+([\d.]+)?/);let f=r.match(/(iPad).*OS\s([\d_]+)/);const p=r.match(/(iPod)(.*OS\s([\d_]+))?/),h=!f&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),g=n==="Win32";let m=n==="MacIntel";const v=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!f&&m&&t.touch&&v.indexOf(`${a}x${d}`)>=0&&(f=r.match(/(Version)\/([\d.]+)/),f||(f=[0,1,"13_0_0"]),m=!1),o&&!g&&(l.os="android",l.android=!0),(f||h||p)&&(l.os="ios",l.ios=!0),l}function Le(i){return i===void 0&&(i={}),fe||(fe=rt(i)),fe}let ue;function nt(){const i=R(),e=Le();let t=!1;function s(){const a=i.navigator.userAgent.toLowerCase();return a.indexOf("safari")>=0&&a.indexOf("chrome")<0&&a.indexOf("android")<0}if(s()){const a=String(i.navigator.userAgent);if(a.includes("Version/")){const[d,o]=a.split("Version/")[1].split(" ")[0].split(".").map(f=>Number(f));t=d<16||d===16&&o<2}}const n=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(i.navigator.userAgent),r=s(),l=r||n&&e.ios;return{isSafari:t||r,needPerspectiveFix:t,need3dFix:l,isWebView:n}}function Ae(){return ue||(ue=nt()),ue}function at(i){let{swiper:e,on:t,emit:s}=i;const n=R();let r=null,l=null;const a=()=>{!e||e.destroyed||!e.initialized||(s("beforeResize"),s("resize"))},d=()=>{!e||e.destroyed||!e.initialized||(r=new ResizeObserver(p=>{l=n.requestAnimationFrame(()=>{const{width:h,height:g}=e;let m=h,v=g;p.forEach(b=>{let{contentBoxSize:S,contentRect:c,target:u}=b;u&&u!==e.el||(m=c?c.width:(S[0]||S).inlineSize,v=c?c.height:(S[0]||S).blockSize)}),(m!==h||v!==g)&&a()})}),r.observe(e.el))},o=()=>{l&&n.cancelAnimationFrame(l),r&&r.unobserve&&e.el&&(r.unobserve(e.el),r=null)},f=()=>{!e||e.destroyed||!e.initialized||s("orientationchange")};t("init",()=>{if(e.params.resizeObserver&&typeof n.ResizeObserver<"u"){d();return}n.addEventListener("resize",a),n.addEventListener("orientationchange",f)}),t("destroy",()=>{o(),n.removeEventListener("resize",a),n.removeEventListener("orientationchange",f)})}function lt(i){let{swiper:e,extendParams:t,on:s,emit:n}=i;const r=[],l=R(),a=function(f,p){p===void 0&&(p={});const h=l.MutationObserver||l.WebkitMutationObserver,g=new h(m=>{if(e.__preventObserver__)return;if(m.length===1){n("observerUpdate",m[0]);return}const v=function(){n("observerUpdate",m[0])};l.requestAnimationFrame?l.requestAnimationFrame(v):l.setTimeout(v,0)});g.observe(f,{attributes:typeof p.attributes>"u"?!0:p.attributes,childList:e.isElement||(typeof p.childList>"u"?!0:p).childList,characterData:typeof p.characterData>"u"?!0:p.characterData}),r.push(g)},d=()=>{if(e.params.observer){if(e.params.observeParents){const f=Ce(e.hostEl);for(let p=0;p<f.length;p+=1)a(f[p])}a(e.hostEl,{childList:e.params.observeSlideChildren}),a(e.wrapperEl,{attributes:!1})}},o=()=>{r.forEach(f=>{f.disconnect()}),r.splice(0,r.length)};t({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",d),s("destroy",o)}var ot={on(i,e,t){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;const n=t?"unshift":"push";return i.split(" ").forEach(r=>{s.eventsListeners[r]||(s.eventsListeners[r]=[]),s.eventsListeners[r][n](e)}),s},once(i,e,t){const s=this;if(!s.eventsListeners||s.destroyed||typeof e!="function")return s;function n(){s.off(i,n),n.__emitterProxy&&delete n.__emitterProxy;for(var r=arguments.length,l=new Array(r),a=0;a<r;a++)l[a]=arguments[a];e.apply(s,l)}return n.__emitterProxy=e,s.on(i,n,t)},onAny(i,e){const t=this;if(!t.eventsListeners||t.destroyed||typeof i!="function")return t;const s=e?"unshift":"push";return t.eventsAnyListeners.indexOf(i)<0&&t.eventsAnyListeners[s](i),t},offAny(i){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const t=e.eventsAnyListeners.indexOf(i);return t>=0&&e.eventsAnyListeners.splice(t,1),e},off(i,e){const t=this;return!t.eventsListeners||t.destroyed||!t.eventsListeners||i.split(" ").forEach(s=>{typeof e>"u"?t.eventsListeners[s]=[]:t.eventsListeners[s]&&t.eventsListeners[s].forEach((n,r)=>{(n===e||n.__emitterProxy&&n.__emitterProxy===e)&&t.eventsListeners[s].splice(r,1)})}),t},emit(){const i=this;if(!i.eventsListeners||i.destroyed||!i.eventsListeners)return i;let e,t,s;for(var n=arguments.length,r=new Array(n),l=0;l<n;l++)r[l]=arguments[l];return typeof r[0]=="string"||Array.isArray(r[0])?(e=r[0],t=r.slice(1,r.length),s=i):(e=r[0].events,t=r[0].data,s=r[0].context||i),t.unshift(s),(Array.isArray(e)?e:e.split(" ")).forEach(d=>{i.eventsAnyListeners&&i.eventsAnyListeners.length&&i.eventsAnyListeners.forEach(o=>{o.apply(s,[d,...t])}),i.eventsListeners&&i.eventsListeners[d]&&i.eventsListeners[d].forEach(o=>{o.apply(s,t)})}),i}};function dt(){const i=this;let e,t;const s=i.el;typeof i.params.width<"u"&&i.params.width!==null?e=i.params.width:e=s.clientWidth,typeof i.params.height<"u"&&i.params.height!==null?t=i.params.height:t=s.clientHeight,!(e===0&&i.isHorizontal()||t===0&&i.isVertical())&&(e=e-parseInt(W(s,"padding-left")||0,10)-parseInt(W(s,"padding-right")||0,10),t=t-parseInt(W(s,"padding-top")||0,10)-parseInt(W(s,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(i,{width:e,height:t,size:i.isHorizontal()?e:t}))}function ct(){const i=this;function e(T,E){return parseFloat(T.getPropertyValue(i.getDirectionLabel(E))||0)}const t=i.params,{wrapperEl:s,slidesEl:n,size:r,rtlTranslate:l,wrongRTL:a}=i,d=i.virtual&&t.virtual.enabled,o=d?i.virtual.slides.length:i.slides.length,f=H(n,`.${i.params.slideClass}, swiper-slide`),p=d?i.virtual.slides.length:f.length;let h=[];const g=[],m=[];let v=t.slidesOffsetBefore;typeof v=="function"&&(v=t.slidesOffsetBefore.call(i));let b=t.slidesOffsetAfter;typeof b=="function"&&(b=t.slidesOffsetAfter.call(i));const S=i.snapGrid.length,c=i.slidesGrid.length;let u=t.spaceBetween,w=-v,y=0,I=0;if(typeof r>"u")return;typeof u=="string"&&u.indexOf("%")>=0?u=parseFloat(u.replace("%",""))/100*r:typeof u=="string"&&(u=parseFloat(u)),i.virtualSize=-u,f.forEach(T=>{l?T.style.marginLeft="":T.style.marginRight="",T.style.marginBottom="",T.style.marginTop=""}),t.centeredSlides&&t.cssMode&&(te(s,"--swiper-centered-offset-before",""),te(s,"--swiper-centered-offset-after",""));const x=t.grid&&t.grid.rows>1&&i.grid;x?i.grid.initSlides(f):i.grid&&i.grid.unsetSlides();let C;const P=t.slidesPerView==="auto"&&t.breakpoints&&Object.keys(t.breakpoints).filter(T=>typeof t.breakpoints[T].slidesPerView<"u").length>0;for(let T=0;T<p;T+=1){C=0;let E;if(f[T]&&(E=f[T]),x&&i.grid.updateSlide(T,E,f),!(f[T]&&W(E,"display")==="none")){if(t.slidesPerView==="auto"){P&&(f[T].style[i.getDirectionLabel("width")]="");const L=getComputedStyle(E),O=E.style.transform,D=E.style.webkitTransform;if(O&&(E.style.transform="none"),D&&(E.style.webkitTransform="none"),t.roundLengths)C=i.isHorizontal()?de(E,"width"):de(E,"height");else{const _=e(L,"width"),A=e(L,"padding-left"),N=e(L,"padding-right"),M=e(L,"margin-left"),k=e(L,"margin-right"),B=L.getPropertyValue("box-sizing");if(B&&B==="border-box")C=_+M+k;else{const{clientWidth:j,offsetWidth:ae}=E;C=_+A+N+M+k+(ae-j)}}O&&(E.style.transform=O),D&&(E.style.webkitTransform=D),t.roundLengths&&(C=Math.floor(C))}else C=(r-(t.slidesPerView-1)*u)/t.slidesPerView,t.roundLengths&&(C=Math.floor(C)),f[T]&&(f[T].style[i.getDirectionLabel("width")]=`${C}px`);f[T]&&(f[T].swiperSlideSize=C),m.push(C),t.centeredSlides?(w=w+C/2+y/2+u,y===0&&T!==0&&(w=w-r/2-u),T===0&&(w=w-r/2-u),Math.abs(w)<1/1e3&&(w=0),t.roundLengths&&(w=Math.floor(w)),I%t.slidesPerGroup===0&&h.push(w),g.push(w)):(t.roundLengths&&(w=Math.floor(w)),(I-Math.min(i.params.slidesPerGroupSkip,I))%i.params.slidesPerGroup===0&&h.push(w),g.push(w),w=w+C+u),i.virtualSize+=C+u,y=C,I+=1}}if(i.virtualSize=Math.max(i.virtualSize,r)+b,l&&a&&(t.effect==="slide"||t.effect==="coverflow")&&(s.style.width=`${i.virtualSize+u}px`),t.setWrapperSize&&(s.style[i.getDirectionLabel("width")]=`${i.virtualSize+u}px`),x&&i.grid.updateWrapperSize(C,h),!t.centeredSlides){const T=[];for(let E=0;E<h.length;E+=1){let L=h[E];t.roundLengths&&(L=Math.floor(L)),h[E]<=i.virtualSize-r&&T.push(L)}h=T,Math.floor(i.virtualSize-r)-Math.floor(h[h.length-1])>1&&h.push(i.virtualSize-r)}if(d&&t.loop){const T=m[0]+u;if(t.slidesPerGroup>1){const E=Math.ceil((i.virtual.slidesBefore+i.virtual.slidesAfter)/t.slidesPerGroup),L=T*t.slidesPerGroup;for(let O=0;O<E;O+=1)h.push(h[h.length-1]+L)}for(let E=0;E<i.virtual.slidesBefore+i.virtual.slidesAfter;E+=1)t.slidesPerGroup===1&&h.push(h[h.length-1]+T),g.push(g[g.length-1]+T),i.virtualSize+=T}if(h.length===0&&(h=[0]),u!==0){const T=i.isHorizontal()&&l?"marginLeft":i.getDirectionLabel("marginRight");f.filter((E,L)=>!t.cssMode||t.loop?!0:L!==f.length-1).forEach(E=>{E.style[T]=`${u}px`})}if(t.centeredSlides&&t.centeredSlidesBounds){let T=0;m.forEach(L=>{T+=L+(u||0)}),T-=u;const E=T>r?T-r:0;h=h.map(L=>L<=0?-v:L>E?E+b:L)}if(t.centerInsufficientSlides){let T=0;m.forEach(L=>{T+=L+(u||0)}),T-=u;const E=(t.slidesOffsetBefore||0)+(t.slidesOffsetAfter||0);if(T+E<r){const L=(r-T-E)/2;h.forEach((O,D)=>{h[D]=O-L}),g.forEach((O,D)=>{g[D]=O+L})}}if(Object.assign(i,{slides:f,snapGrid:h,slidesGrid:g,slidesSizesGrid:m}),t.centeredSlides&&t.cssMode&&!t.centeredSlidesBounds){te(s,"--swiper-centered-offset-before",`${-h[0]}px`),te(s,"--swiper-centered-offset-after",`${i.size/2-m[m.length-1]/2}px`);const T=-i.snapGrid[0],E=-i.slidesGrid[0];i.snapGrid=i.snapGrid.map(L=>L+T),i.slidesGrid=i.slidesGrid.map(L=>L+E)}if(p!==o&&i.emit("slidesLengthChange"),h.length!==S&&(i.params.watchOverflow&&i.checkOverflow(),i.emit("snapGridLengthChange")),g.length!==c&&i.emit("slidesGridLengthChange"),t.watchSlidesProgress&&i.updateSlidesOffset(),i.emit("slidesUpdated"),!d&&!t.cssMode&&(t.effect==="slide"||t.effect==="fade")){const T=`${t.containerModifierClass}backface-hidden`,E=i.el.classList.contains(T);p<=t.maxBackfaceHiddenSlides?E||i.el.classList.add(T):E&&i.el.classList.remove(T)}}function ft(i){const e=this,t=[],s=e.virtual&&e.params.virtual.enabled;let n=0,r;typeof i=="number"?e.setTransition(i):i===!0&&e.setTransition(e.params.speed);const l=a=>s?e.slides[e.getSlideIndexByData(a)]:e.slides[a];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(a=>{t.push(a)});else for(r=0;r<Math.ceil(e.params.slidesPerView);r+=1){const a=e.activeIndex+r;if(a>e.slides.length&&!s)break;t.push(l(a))}else t.push(l(e.activeIndex));for(r=0;r<t.length;r+=1)if(typeof t[r]<"u"){const a=t[r].offsetHeight;n=a>n?a:n}(n||n===0)&&(e.wrapperEl.style.height=`${n}px`)}function ut(){const i=this,e=i.slides,t=i.isElement?i.isHorizontal()?i.wrapperEl.offsetLeft:i.wrapperEl.offsetTop:0;for(let s=0;s<e.length;s+=1)e[s].swiperSlideOffset=(i.isHorizontal()?e[s].offsetLeft:e[s].offsetTop)-t-i.cssOverflowAdjustment()}const Oe=(i,e,t)=>{e&&!i.classList.contains(t)?i.classList.add(t):!e&&i.classList.contains(t)&&i.classList.remove(t)};function pt(i){i===void 0&&(i=this&&this.translate||0);const e=this,t=e.params,{slides:s,rtlTranslate:n,snapGrid:r}=e;if(s.length===0)return;typeof s[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let l=-i;n&&(l=i),e.visibleSlidesIndexes=[],e.visibleSlides=[];let a=t.spaceBetween;typeof a=="string"&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*e.size:typeof a=="string"&&(a=parseFloat(a));for(let d=0;d<s.length;d+=1){const o=s[d];let f=o.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(f-=s[0].swiperSlideOffset);const p=(l+(t.centeredSlides?e.minTranslate():0)-f)/(o.swiperSlideSize+a),h=(l-r[0]+(t.centeredSlides?e.minTranslate():0)-f)/(o.swiperSlideSize+a),g=-(l-f),m=g+e.slidesSizesGrid[d],v=g>=0&&g<=e.size-e.slidesSizesGrid[d],b=g>=0&&g<e.size-1||m>1&&m<=e.size||g<=0&&m>=e.size;b&&(e.visibleSlides.push(o),e.visibleSlidesIndexes.push(d)),Oe(o,b,t.slideVisibleClass),Oe(o,v,t.slideFullyVisibleClass),o.progress=n?-p:p,o.originalProgress=n?-h:h}}function mt(i){const e=this;if(typeof i>"u"){const f=e.rtlTranslate?-1:1;i=e&&e.translate&&e.translate*f||0}const t=e.params,s=e.maxTranslate()-e.minTranslate();let{progress:n,isBeginning:r,isEnd:l,progressLoop:a}=e;const d=r,o=l;if(s===0)n=0,r=!0,l=!0;else{n=(i-e.minTranslate())/s;const f=Math.abs(i-e.minTranslate())<1,p=Math.abs(i-e.maxTranslate())<1;r=f||n<=0,l=p||n>=1,f&&(n=0),p&&(n=1)}if(t.loop){const f=e.getSlideIndexByData(0),p=e.getSlideIndexByData(e.slides.length-1),h=e.slidesGrid[f],g=e.slidesGrid[p],m=e.slidesGrid[e.slidesGrid.length-1],v=Math.abs(i);v>=h?a=(v-h)/m:a=(v+m-g)/m,a>1&&(a-=1)}Object.assign(e,{progress:n,progressLoop:a,isBeginning:r,isEnd:l}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&e.updateSlidesProgress(i),r&&!d&&e.emit("reachBeginning toEdge"),l&&!o&&e.emit("reachEnd toEdge"),(d&&!r||o&&!l)&&e.emit("fromEdge"),e.emit("progress",n)}const pe=(i,e,t)=>{e&&!i.classList.contains(t)?i.classList.add(t):!e&&i.classList.contains(t)&&i.classList.remove(t)};function ht(){const i=this,{slides:e,params:t,slidesEl:s,activeIndex:n}=i,r=i.virtual&&t.virtual.enabled,l=i.grid&&t.grid&&t.grid.rows>1,a=p=>H(s,`.${t.slideClass}${p}, swiper-slide${p}`)[0];let d,o,f;if(r)if(t.loop){let p=n-i.virtual.slidesBefore;p<0&&(p=i.virtual.slides.length+p),p>=i.virtual.slides.length&&(p-=i.virtual.slides.length),d=a(`[data-swiper-slide-index="${p}"]`)}else d=a(`[data-swiper-slide-index="${n}"]`);else l?(d=e.find(p=>p.column===n),f=e.find(p=>p.column===n+1),o=e.find(p=>p.column===n-1)):d=e[n];d&&(l||(f=tt(d,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!f&&(f=e[0]),o=et(d,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!o===0&&(o=e[e.length-1]))),e.forEach(p=>{pe(p,p===d,t.slideActiveClass),pe(p,p===f,t.slideNextClass),pe(p,p===o,t.slidePrevClass)}),i.emitSlidesClasses()}const re=(i,e)=>{if(!i||i.destroyed||!i.params)return;const t=()=>i.isElement?"swiper-slide":`.${i.params.slideClass}`,s=e.closest(t());if(s){let n=s.querySelector(`.${i.params.lazyPreloaderClass}`);!n&&i.isElement&&(s.shadowRoot?n=s.shadowRoot.querySelector(`.${i.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{s.shadowRoot&&(n=s.shadowRoot.querySelector(`.${i.params.lazyPreloaderClass}`),n&&n.remove())})),n&&n.remove()}},me=(i,e)=>{if(!i.slides[e])return;const t=i.slides[e].querySelector('[loading="lazy"]');t&&t.removeAttribute("loading")},he=i=>{if(!i||i.destroyed||!i.params)return;let e=i.params.lazyPreloadPrevNext;const t=i.slides.length;if(!t||!e||e<0)return;e=Math.min(e,t);const s=i.params.slidesPerView==="auto"?i.slidesPerViewDynamic():Math.ceil(i.params.slidesPerView),n=i.activeIndex;if(i.params.grid&&i.params.grid.rows>1){const l=n,a=[l-e];a.push(...Array.from({length:e}).map((d,o)=>l+s+o)),i.slides.forEach((d,o)=>{a.includes(d.column)&&me(i,o)});return}const r=n+s-1;if(i.params.rewind||i.params.loop)for(let l=n-e;l<=r+e;l+=1){const a=(l%t+t)%t;(a<n||a>r)&&me(i,a)}else for(let l=Math.max(n-e,0);l<=Math.min(r+e,t-1);l+=1)l!==n&&(l>r||l<n)&&me(i,l)};function gt(i){const{slidesGrid:e,params:t}=i,s=i.rtlTranslate?i.translate:-i.translate;let n;for(let r=0;r<e.length;r+=1)typeof e[r+1]<"u"?s>=e[r]&&s<e[r+1]-(e[r+1]-e[r])/2?n=r:s>=e[r]&&s<e[r+1]&&(n=r+1):s>=e[r]&&(n=r);return t.normalizeSlideIndex&&(n<0||typeof n>"u")&&(n=0),n}function vt(i){const e=this,t=e.rtlTranslate?e.translate:-e.translate,{snapGrid:s,params:n,activeIndex:r,realIndex:l,snapIndex:a}=e;let d=i,o;const f=g=>{let m=g-e.virtual.slidesBefore;return m<0&&(m=e.virtual.slides.length+m),m>=e.virtual.slides.length&&(m-=e.virtual.slides.length),m};if(typeof d>"u"&&(d=gt(e)),s.indexOf(t)>=0)o=s.indexOf(t);else{const g=Math.min(n.slidesPerGroupSkip,d);o=g+Math.floor((d-g)/n.slidesPerGroup)}if(o>=s.length&&(o=s.length-1),d===r&&!e.params.loop){o!==a&&(e.snapIndex=o,e.emit("snapIndexChange"));return}if(d===r&&e.params.loop&&e.virtual&&e.params.virtual.enabled){e.realIndex=f(d);return}const p=e.grid&&n.grid&&n.grid.rows>1;let h;if(e.virtual&&n.virtual.enabled&&n.loop)h=f(d);else if(p){const g=e.slides.find(v=>v.column===d);let m=parseInt(g.getAttribute("data-swiper-slide-index"),10);Number.isNaN(m)&&(m=Math.max(e.slides.indexOf(g),0)),h=Math.floor(m/n.grid.rows)}else if(e.slides[d]){const g=e.slides[d].getAttribute("data-swiper-slide-index");g?h=parseInt(g,10):h=d}else h=d;Object.assign(e,{previousSnapIndex:a,snapIndex:o,previousRealIndex:l,realIndex:h,previousIndex:r,activeIndex:d}),e.initialized&&he(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(l!==h&&e.emit("realIndexChange"),e.emit("slideChange"))}function wt(i,e){const t=this,s=t.params;let n=i.closest(`.${s.slideClass}, swiper-slide`);!n&&t.isElement&&e&&e.length>1&&e.includes(i)&&[...e.slice(e.indexOf(i)+1,e.length)].forEach(a=>{!n&&a.matches&&a.matches(`.${s.slideClass}, swiper-slide`)&&(n=a)});let r=!1,l;if(n){for(let a=0;a<t.slides.length;a+=1)if(t.slides[a]===n){r=!0,l=a;break}}if(n&&r)t.clickedSlide=n,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(n.getAttribute("data-swiper-slide-index"),10):t.clickedIndex=l;else{t.clickedSlide=void 0,t.clickedIndex=void 0;return}s.slideToClickedSlide&&t.clickedIndex!==void 0&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}var St={updateSize:dt,updateSlides:ct,updateAutoHeight:ft,updateSlidesOffset:ut,updateSlidesProgress:pt,updateProgress:mt,updateSlidesClasses:ht,updateActiveIndex:vt,updateClickedSlide:wt};function yt(i){i===void 0&&(i=this.isHorizontal()?"x":"y");const e=this,{params:t,rtlTranslate:s,translate:n,wrapperEl:r}=e;if(t.virtualTranslate)return s?-n:n;if(t.cssMode)return n;let l=Ke(r,i);return l+=e.cssOverflowAdjustment(),s&&(l=-l),l||0}function Tt(i,e){const t=this,{rtlTranslate:s,params:n,wrapperEl:r,progress:l}=t;let a=0,d=0;const o=0;t.isHorizontal()?a=s?-i:i:d=i,n.roundLengths&&(a=Math.floor(a),d=Math.floor(d)),t.previousTranslate=t.translate,t.translate=t.isHorizontal()?a:d,n.cssMode?r[t.isHorizontal()?"scrollLeft":"scrollTop"]=t.isHorizontal()?-a:-d:n.virtualTranslate||(t.isHorizontal()?a-=t.cssOverflowAdjustment():d-=t.cssOverflowAdjustment(),r.style.transform=`translate3d(${a}px, ${d}px, ${o}px)`);let f;const p=t.maxTranslate()-t.minTranslate();p===0?f=0:f=(i-t.minTranslate())/p,f!==l&&t.updateProgress(i),t.emit("setTranslate",t.translate,e)}function Et(){return-this.snapGrid[0]}function bt(){return-this.snapGrid[this.snapGrid.length-1]}function xt(i,e,t,s,n){i===void 0&&(i=0),e===void 0&&(e=this.params.speed),t===void 0&&(t=!0),s===void 0&&(s=!0);const r=this,{params:l,wrapperEl:a}=r;if(r.animating&&l.preventInteractionOnTransition)return!1;const d=r.minTranslate(),o=r.maxTranslate();let f;if(s&&i>d?f=d:s&&i<o?f=o:f=i,r.updateProgress(f),l.cssMode){const p=r.isHorizontal();if(e===0)a[p?"scrollLeft":"scrollTop"]=-f;else{if(!r.support.smoothScroll)return Ie({swiper:r,targetPosition:-f,side:p?"left":"top"}),!0;a.scrollTo({[p?"left":"top"]:-f,behavior:"smooth"})}return!0}return e===0?(r.setTransition(0),r.setTranslate(f),t&&(r.emit("beforeTransitionStart",e,n),r.emit("transitionEnd"))):(r.setTransition(e),r.setTranslate(f),t&&(r.emit("beforeTransitionStart",e,n),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(h){!r||r.destroyed||h.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,r.animating=!1,t&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}var It={getTranslate:yt,setTranslate:Tt,minTranslate:Et,maxTranslate:bt,translateTo:xt};function Ct(i,e){const t=this;t.params.cssMode||(t.wrapperEl.style.transitionDuration=`${i}ms`,t.wrapperEl.style.transitionDelay=i===0?"0ms":""),t.emit("setTransition",i,e)}function De(i){let{swiper:e,runCallbacks:t,direction:s,step:n}=i;const{activeIndex:r,previousIndex:l}=e;let a=s;a||(r>l?a="next":r<l?a="prev":a="reset"),e.emit(`transition${n}`),t&&a==="reset"?e.emit(`slideResetTransition${n}`):t&&r!==l&&(e.emit(`slideChangeTransition${n}`),a==="next"?e.emit(`slideNextTransition${n}`):e.emit(`slidePrevTransition${n}`))}function Mt(i,e){i===void 0&&(i=!0);const t=this,{params:s}=t;s.cssMode||(s.autoHeight&&t.updateAutoHeight(),De({swiper:t,runCallbacks:i,direction:e,step:"Start"}))}function Pt(i,e){i===void 0&&(i=!0);const t=this,{params:s}=t;t.animating=!1,!s.cssMode&&(t.setTransition(0),De({swiper:t,runCallbacks:i,direction:e,step:"End"}))}var Lt={setTransition:Ct,transitionStart:Mt,transitionEnd:Pt};function At(i,e,t,s,n){i===void 0&&(i=0),t===void 0&&(t=!0),typeof i=="string"&&(i=parseInt(i,10));const r=this;let l=i;l<0&&(l=0);const{params:a,snapGrid:d,slidesGrid:o,previousIndex:f,activeIndex:p,rtlTranslate:h,wrapperEl:g,enabled:m}=r;if(!m&&!s&&!n||r.destroyed||r.animating&&a.preventInteractionOnTransition)return!1;typeof e>"u"&&(e=r.params.speed);const v=Math.min(r.params.slidesPerGroupSkip,l);let b=v+Math.floor((l-v)/r.params.slidesPerGroup);b>=d.length&&(b=d.length-1);const S=-d[b];if(a.normalizeSlideIndex)for(let x=0;x<o.length;x+=1){const C=-Math.floor(S*100),P=Math.floor(o[x]*100),T=Math.floor(o[x+1]*100);typeof o[x+1]<"u"?C>=P&&C<T-(T-P)/2?l=x:C>=P&&C<T&&(l=x+1):C>=P&&(l=x)}if(r.initialized&&l!==p&&(!r.allowSlideNext&&(h?S>r.translate&&S>r.minTranslate():S<r.translate&&S<r.minTranslate())||!r.allowSlidePrev&&S>r.translate&&S>r.maxTranslate()&&(p||0)!==l))return!1;l!==(f||0)&&t&&r.emit("beforeSlideChangeStart"),r.updateProgress(S);let c;l>p?c="next":l<p?c="prev":c="reset";const u=r.virtual&&r.params.virtual.enabled;if(!(u&&n)&&(h&&-S===r.translate||!h&&S===r.translate))return r.updateActiveIndex(l),a.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),a.effect!=="slide"&&r.setTranslate(S),c!=="reset"&&(r.transitionStart(t,c),r.transitionEnd(t,c)),!1;if(a.cssMode){const x=r.isHorizontal(),C=h?S:-S;if(e===0)u&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),u&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{g[x?"scrollLeft":"scrollTop"]=C})):g[x?"scrollLeft":"scrollTop"]=C,u&&requestAnimationFrame(()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1});else{if(!r.support.smoothScroll)return Ie({swiper:r,targetPosition:C,side:x?"left":"top"}),!0;g.scrollTo({[x?"left":"top"]:C,behavior:"smooth"})}return!0}const I=Ae().isSafari;return u&&!n&&I&&r.isElement&&r.virtual.update(!1,!1,l),r.setTransition(e),r.setTranslate(S),r.updateActiveIndex(l),r.updateSlidesClasses(),r.emit("beforeTransitionStart",e,s),r.transitionStart(t,c),e===0?r.transitionEnd(t,c):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(C){!r||r.destroyed||C.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(t,c))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0}function Ot(i,e,t,s){i===void 0&&(i=0),t===void 0&&(t=!0),typeof i=="string"&&(i=parseInt(i,10));const n=this;if(n.destroyed)return;typeof e>"u"&&(e=n.params.speed);const r=n.grid&&n.params.grid&&n.params.grid.rows>1;let l=i;if(n.params.loop)if(n.virtual&&n.params.virtual.enabled)l=l+n.virtual.slidesBefore;else{let a;if(r){const h=l*n.params.grid.rows;a=n.slides.find(g=>g.getAttribute("data-swiper-slide-index")*1===h).column}else a=n.getSlideIndexByData(l);const d=r?Math.ceil(n.slides.length/n.params.grid.rows):n.slides.length,{centeredSlides:o}=n.params;let f=n.params.slidesPerView;f==="auto"?f=n.slidesPerViewDynamic():(f=Math.ceil(parseFloat(n.params.slidesPerView,10)),o&&f%2===0&&(f=f+1));let p=d-a<f;if(o&&(p=p||a<Math.ceil(f/2)),s&&o&&n.params.slidesPerView!=="auto"&&!r&&(p=!1),p){const h=o?a<n.activeIndex?"prev":"next":a-n.activeIndex-1<n.params.slidesPerView?"next":"prev";n.loopFix({direction:h,slideTo:!0,activeSlideIndex:h==="next"?a+1:a-d+1,slideRealIndex:h==="next"?n.realIndex:void 0})}if(r){const h=l*n.params.grid.rows;l=n.slides.find(g=>g.getAttribute("data-swiper-slide-index")*1===h).column}else l=n.getSlideIndexByData(l)}return requestAnimationFrame(()=>{n.slideTo(l,e,t,s)}),n}function Dt(i,e,t){e===void 0&&(e=!0);const s=this,{enabled:n,params:r,animating:l}=s;if(!n||s.destroyed)return s;typeof i>"u"&&(i=s.params.speed);let a=r.slidesPerGroup;r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(a=Math.max(s.slidesPerViewDynamic("current",!0),1));const d=s.activeIndex<r.slidesPerGroupSkip?1:a,o=s.virtual&&r.virtual.enabled;if(r.loop){if(l&&!o&&r.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&r.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+d,i,e,t)}),!0}return r.rewind&&s.isEnd?s.slideTo(0,i,e,t):s.slideTo(s.activeIndex+d,i,e,t)}function _t(i,e,t){e===void 0&&(e=!0);const s=this,{params:n,snapGrid:r,slidesGrid:l,rtlTranslate:a,enabled:d,animating:o}=s;if(!d||s.destroyed)return s;typeof i>"u"&&(i=s.params.speed);const f=s.virtual&&n.virtual.enabled;if(n.loop){if(o&&!f&&n.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}const p=a?s.translate:-s.translate;function h(c){return c<0?-Math.floor(Math.abs(c)):Math.floor(c)}const g=h(p),m=r.map(c=>h(c)),v=n.freeMode&&n.freeMode.enabled;let b=r[m.indexOf(g)-1];if(typeof b>"u"&&(n.cssMode||v)){let c;r.forEach((u,w)=>{g>=u&&(c=w)}),typeof c<"u"&&(b=v?r[c]:r[c>0?c-1:c])}let S=0;if(typeof b<"u"&&(S=l.indexOf(b),S<0&&(S=s.activeIndex-1),n.slidesPerView==="auto"&&n.slidesPerGroup===1&&n.slidesPerGroupAuto&&(S=S-s.slidesPerViewDynamic("previous",!0)+1,S=Math.max(S,0))),n.rewind&&s.isBeginning){const c=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(c,i,e,t)}else if(n.loop&&s.activeIndex===0&&n.cssMode)return requestAnimationFrame(()=>{s.slideTo(S,i,e,t)}),!0;return s.slideTo(S,i,e,t)}function zt(i,e,t){e===void 0&&(e=!0);const s=this;if(!s.destroyed)return typeof i>"u"&&(i=s.params.speed),s.slideTo(s.activeIndex,i,e,t)}function Nt(i,e,t,s){e===void 0&&(e=!0),s===void 0&&(s=.5);const n=this;if(n.destroyed)return;typeof i>"u"&&(i=n.params.speed);let r=n.activeIndex;const l=Math.min(n.params.slidesPerGroupSkip,r),a=l+Math.floor((r-l)/n.params.slidesPerGroup),d=n.rtlTranslate?n.translate:-n.translate;if(d>=n.snapGrid[a]){const o=n.snapGrid[a],f=n.snapGrid[a+1];d-o>(f-o)*s&&(r+=n.params.slidesPerGroup)}else{const o=n.snapGrid[a-1],f=n.snapGrid[a];d-o<=(f-o)*s&&(r-=n.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,n.slidesGrid.length-1),n.slideTo(r,i,e,t)}function kt(){const i=this;if(i.destroyed)return;const{params:e,slidesEl:t}=i,s=e.slidesPerView==="auto"?i.slidesPerViewDynamic():e.slidesPerView;let n=i.getSlideIndexWhenGrid(i.clickedIndex),r;const l=i.isElement?"swiper-slide":`.${e.slideClass}`,a=i.grid&&i.params.grid&&i.params.grid.rows>1;if(e.loop){if(i.animating)return;r=parseInt(i.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?i.slideToLoop(r):n>(a?(i.slides.length-s)/2-(i.params.grid.rows-1):i.slides.length-s)?(i.loopFix(),n=i.getSlideIndex(H(t,`${l}[data-swiper-slide-index="${r}"]`)[0]),xe(()=>{i.slideTo(n)})):i.slideTo(n)}else i.slideTo(n)}var Gt={slideTo:At,slideToLoop:Ot,slideNext:Dt,slidePrev:_t,slideReset:zt,slideToClosest:Nt,slideToClickedSlide:kt};function Rt(i,e){const t=this,{params:s,slidesEl:n}=t;if(!s.loop||t.virtual&&t.params.virtual.enabled)return;const r=()=>{H(n,`.${s.slideClass}, swiper-slide`).forEach((g,m)=>{g.setAttribute("data-swiper-slide-index",m)})},l=()=>{const h=H(n,`.${s.slideBlankClass}`);h.forEach(g=>{g.remove()}),h.length>0&&(t.recalcSlides(),t.updateSlides())},a=t.grid&&s.grid&&s.grid.rows>1;s.loopAddBlankSlides&&(s.slidesPerGroup>1||a)&&l();const d=s.slidesPerGroup*(a?s.grid.rows:1),o=t.slides.length%d!==0,f=a&&t.slides.length%s.grid.rows!==0,p=h=>{for(let g=0;g<h;g+=1){const m=t.isElement?Q("swiper-slide",[s.slideBlankClass]):Q("div",[s.slideClass,s.slideBlankClass]);t.slidesEl.append(m)}};if(o){if(s.loopAddBlankSlides){const h=d-t.slides.length%d;p(h),t.recalcSlides(),t.updateSlides()}else ie("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else if(f){if(s.loopAddBlankSlides){const h=s.grid.rows-t.slides.length%s.grid.rows;p(h),t.recalcSlides(),t.updateSlides()}else ie("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else r();t.loopFix({slideRealIndex:i,direction:s.centeredSlides?void 0:"next",initial:e})}function Bt(i){let{slideRealIndex:e,slideTo:t=!0,direction:s,setTranslate:n,activeSlideIndex:r,initial:l,byController:a,byMousewheel:d}=i===void 0?{}:i;const o=this;if(!o.params.loop)return;o.emit("beforeLoopFix");const{slides:f,allowSlidePrev:p,allowSlideNext:h,slidesEl:g,params:m}=o,{centeredSlides:v,initialSlide:b}=m;if(o.allowSlidePrev=!0,o.allowSlideNext=!0,o.virtual&&m.virtual.enabled){t&&(!m.centeredSlides&&o.snapIndex===0?o.slideTo(o.virtual.slides.length,0,!1,!0):m.centeredSlides&&o.snapIndex<m.slidesPerView?o.slideTo(o.virtual.slides.length+o.snapIndex,0,!1,!0):o.snapIndex===o.snapGrid.length-1&&o.slideTo(o.virtual.slidesBefore,0,!1,!0)),o.allowSlidePrev=p,o.allowSlideNext=h,o.emit("loopFix");return}let S=m.slidesPerView;S==="auto"?S=o.slidesPerViewDynamic():(S=Math.ceil(parseFloat(m.slidesPerView,10)),v&&S%2===0&&(S=S+1));const c=m.slidesPerGroupAuto?S:m.slidesPerGroup;let u=v?Math.max(c,Math.ceil(S/2)):c;u%c!==0&&(u+=c-u%c),u+=m.loopAdditionalSlides,o.loopedSlides=u;const w=o.grid&&m.grid&&m.grid.rows>1;f.length<S+u||o.params.effect==="cards"&&f.length<S+u*2?ie("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):w&&m.grid.fill==="row"&&ie("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const y=[],I=[],x=w?Math.ceil(f.length/m.grid.rows):f.length,C=l&&x-b<S&&!v;let P=C?b:o.activeIndex;typeof r>"u"?r=o.getSlideIndex(f.find(A=>A.classList.contains(m.slideActiveClass))):P=r;const T=s==="next"||!s,E=s==="prev"||!s;let L=0,O=0;const _=(w?f[r].column:r)+(v&&typeof n>"u"?-S/2+.5:0);if(_<u){L=Math.max(u-_,c);for(let A=0;A<u-_;A+=1){const N=A-Math.floor(A/x)*x;if(w){const M=x-N-1;for(let k=f.length-1;k>=0;k-=1)f[k].column===M&&y.push(k)}else y.push(x-N-1)}}else if(_+S>x-u){O=Math.max(_-(x-u*2),c),C&&(O=Math.max(O,S-x+b+1));for(let A=0;A<O;A+=1){const N=A-Math.floor(A/x)*x;w?f.forEach((M,k)=>{M.column===N&&I.push(k)}):I.push(N)}}if(o.__preventObserver__=!0,requestAnimationFrame(()=>{o.__preventObserver__=!1}),o.params.effect==="cards"&&f.length<S+u*2&&(I.includes(r)&&I.splice(I.indexOf(r),1),y.includes(r)&&y.splice(y.indexOf(r),1)),E&&y.forEach(A=>{f[A].swiperLoopMoveDOM=!0,g.prepend(f[A]),f[A].swiperLoopMoveDOM=!1}),T&&I.forEach(A=>{f[A].swiperLoopMoveDOM=!0,g.append(f[A]),f[A].swiperLoopMoveDOM=!1}),o.recalcSlides(),m.slidesPerView==="auto"?o.updateSlides():w&&(y.length>0&&E||I.length>0&&T)&&o.slides.forEach((A,N)=>{o.grid.updateSlide(N,A,o.slides)}),m.watchSlidesProgress&&o.updateSlidesOffset(),t){if(y.length>0&&E){if(typeof e>"u"){const A=o.slidesGrid[P],M=o.slidesGrid[P+L]-A;d?o.setTranslate(o.translate-M):(o.slideTo(P+Math.ceil(L),0,!1,!0),n&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-M,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-M))}else if(n){const A=w?y.length/m.grid.rows:y.length;o.slideTo(o.activeIndex+A,0,!1,!0),o.touchEventsData.currentTranslate=o.translate}}else if(I.length>0&&T)if(typeof e>"u"){const A=o.slidesGrid[P],M=o.slidesGrid[P-O]-A;d?o.setTranslate(o.translate-M):(o.slideTo(P-O,0,!1,!0),n&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-M,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-M))}else{const A=w?I.length/m.grid.rows:I.length;o.slideTo(o.activeIndex-A,0,!1,!0)}}if(o.allowSlidePrev=p,o.allowSlideNext=h,o.controller&&o.controller.control&&!a){const A={slideRealIndex:e,direction:s,setTranslate:n,activeSlideIndex:r,byController:!0};Array.isArray(o.controller.control)?o.controller.control.forEach(N=>{!N.destroyed&&N.params.loop&&N.loopFix({...A,slideTo:N.params.slidesPerView===m.slidesPerView?t:!1})}):o.controller.control instanceof o.constructor&&o.controller.control.params.loop&&o.controller.control.loopFix({...A,slideTo:o.controller.control.params.slidesPerView===m.slidesPerView?t:!1})}o.emit("loopFix")}function $t(){const i=this,{params:e,slidesEl:t}=i;if(!e.loop||!t||i.virtual&&i.params.virtual.enabled)return;i.recalcSlides();const s=[];i.slides.forEach(n=>{const r=typeof n.swiperSlideIndex>"u"?n.getAttribute("data-swiper-slide-index")*1:n.swiperSlideIndex;s[r]=n}),i.slides.forEach(n=>{n.removeAttribute("data-swiper-slide-index")}),s.forEach(n=>{t.append(n)}),i.recalcSlides(),i.slideTo(i.realIndex,0)}var Vt={loopCreate:Rt,loopFix:Bt,loopDestroy:$t};function Ft(i){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const t=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),t.style.cursor="move",t.style.cursor=i?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function Ht(){const i=this;i.params.watchOverflow&&i.isLocked||i.params.cssMode||(i.isElement&&(i.__preventObserver__=!0),i[i.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",i.isElement&&requestAnimationFrame(()=>{i.__preventObserver__=!1}))}var Wt={setGrabCursor:Ft,unsetGrabCursor:Ht};function qt(i,e){e===void 0&&(e=this);function t(s){if(!s||s===F()||s===R())return null;s.assignedSlot&&(s=s.assignedSlot);const n=s.closest(i);return!n&&!s.getRootNode?null:n||t(s.getRootNode().host)}return t(e)}function _e(i,e,t){const s=R(),{params:n}=i,r=n.edgeSwipeDetection,l=n.edgeSwipeThreshold;return r&&(t<=l||t>=s.innerWidth-l)?r==="prevent"?(e.preventDefault(),!0):!1:!0}function Xt(i){const e=this,t=F();let s=i;s.originalEvent&&(s=s.originalEvent);const n=e.touchEventsData;if(s.type==="pointerdown"){if(n.pointerId!==null&&n.pointerId!==s.pointerId)return;n.pointerId=s.pointerId}else s.type==="touchstart"&&s.targetTouches.length===1&&(n.touchId=s.targetTouches[0].identifier);if(s.type==="touchstart"){_e(e,s,s.targetTouches[0].pageX);return}const{params:r,touches:l,enabled:a}=e;if(!a||!r.simulateTouch&&s.pointerType==="mouse"||e.animating&&r.preventInteractionOnTransition)return;!e.animating&&r.cssMode&&r.loop&&e.loopFix();let d=s.target;if(r.touchEventsTarget==="wrapper"&&!Je(d,e.wrapperEl)||"which"in s&&s.which===3||"button"in s&&s.button>0||n.isTouched&&n.isMoved)return;const o=!!r.noSwipingClass&&r.noSwipingClass!=="",f=s.composedPath?s.composedPath():s.path;o&&s.target&&s.target.shadowRoot&&f&&(d=f[0]);const p=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,h=!!(s.target&&s.target.shadowRoot);if(r.noSwiping&&(h?qt(p,d):d.closest(p))){e.allowClick=!0;return}if(r.swipeHandler&&!d.closest(r.swipeHandler))return;l.currentX=s.pageX,l.currentY=s.pageY;const g=l.currentX,m=l.currentY;if(!_e(e,s,g))return;Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),l.startX=g,l.startY=m,n.touchStartTime=J(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,r.threshold>0&&(n.allowThresholdMove=!1);let v=!0;d.matches(n.focusableElements)&&(v=!1,d.nodeName==="SELECT"&&(n.isTouched=!1)),t.activeElement&&t.activeElement.matches(n.focusableElements)&&t.activeElement!==d&&(s.pointerType==="mouse"||s.pointerType!=="mouse"&&!d.matches(n.focusableElements))&&t.activeElement.blur();const b=v&&e.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||b)&&!d.isContentEditable&&s.preventDefault(),r.freeMode&&r.freeMode.enabled&&e.freeMode&&e.animating&&!r.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",s)}function jt(i){const e=F(),t=this,s=t.touchEventsData,{params:n,touches:r,rtlTranslate:l,enabled:a}=t;if(!a||!n.simulateTouch&&i.pointerType==="mouse")return;let d=i;if(d.originalEvent&&(d=d.originalEvent),d.type==="pointermove"&&(s.touchId!==null||d.pointerId!==s.pointerId))return;let o;if(d.type==="touchmove"){if(o=[...d.changedTouches].find(y=>y.identifier===s.touchId),!o||o.identifier!==s.touchId)return}else o=d;if(!s.isTouched){s.startMoving&&s.isScrolling&&t.emit("touchMoveOpposite",d);return}const f=o.pageX,p=o.pageY;if(d.preventedByNestedSwiper){r.startX=f,r.startY=p;return}if(!t.allowTouchMove){d.target.matches(s.focusableElements)||(t.allowClick=!1),s.isTouched&&(Object.assign(r,{startX:f,startY:p,currentX:f,currentY:p}),s.touchStartTime=J());return}if(n.touchReleaseOnEdges&&!n.loop)if(t.isVertical()){if(p<r.startY&&t.translate<=t.maxTranslate()||p>r.startY&&t.translate>=t.minTranslate()){s.isTouched=!1,s.isMoved=!1;return}}else{if(l&&(f>r.startX&&-t.translate<=t.maxTranslate()||f<r.startX&&-t.translate>=t.minTranslate()))return;if(!l&&(f<r.startX&&t.translate<=t.maxTranslate()||f>r.startX&&t.translate>=t.minTranslate()))return}if(e.activeElement&&e.activeElement.matches(s.focusableElements)&&e.activeElement!==d.target&&d.pointerType!=="mouse"&&e.activeElement.blur(),e.activeElement&&d.target===e.activeElement&&d.target.matches(s.focusableElements)){s.isMoved=!0,t.allowClick=!1;return}s.allowTouchCallbacks&&t.emit("touchMove",d),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=f,r.currentY=p;const h=r.currentX-r.startX,g=r.currentY-r.startY;if(t.params.threshold&&Math.sqrt(h**2+g**2)<t.params.threshold)return;if(typeof s.isScrolling>"u"){let y;t.isHorizontal()&&r.currentY===r.startY||t.isVertical()&&r.currentX===r.startX?s.isScrolling=!1:h*h+g*g>=25&&(y=Math.atan2(Math.abs(g),Math.abs(h))*180/Math.PI,s.isScrolling=t.isHorizontal()?y>n.touchAngle:90-y>n.touchAngle)}if(s.isScrolling&&t.emit("touchMoveOpposite",d),typeof s.startMoving>"u"&&(r.currentX!==r.startX||r.currentY!==r.startY)&&(s.startMoving=!0),s.isScrolling||d.type==="touchmove"&&s.preventTouchMoveFromPointerMove){s.isTouched=!1;return}if(!s.startMoving)return;t.allowClick=!1,!n.cssMode&&d.cancelable&&d.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&d.stopPropagation();let m=t.isHorizontal()?h:g,v=t.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;n.oneWayMovement&&(m=Math.abs(m)*(l?1:-1),v=Math.abs(v)*(l?1:-1)),r.diff=m,m*=n.touchRatio,l&&(m=-m,v=-v);const b=t.touchesDirection;t.swipeDirection=m>0?"prev":"next",t.touchesDirection=v>0?"prev":"next";const S=t.params.loop&&!n.cssMode,c=t.touchesDirection==="next"&&t.allowSlideNext||t.touchesDirection==="prev"&&t.allowSlidePrev;if(!s.isMoved){if(S&&c&&t.loopFix({direction:t.swipeDirection}),s.startTranslate=t.getTranslate(),t.setTransition(0),t.animating){const y=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});t.wrapperEl.dispatchEvent(y)}s.allowMomentumBounce=!1,n.grabCursor&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!0),t.emit("sliderFirstMove",d)}if(new Date().getTime(),n._loopSwapReset!==!1&&s.isMoved&&s.allowThresholdMove&&b!==t.touchesDirection&&S&&c&&Math.abs(m)>=1){Object.assign(r,{startX:f,startY:p,currentX:f,currentY:p,startTranslate:s.currentTranslate}),s.loopSwapReset=!0,s.startTranslate=s.currentTranslate;return}t.emit("sliderMove",d),s.isMoved=!0,s.currentTranslate=m+s.startTranslate;let u=!0,w=n.resistanceRatio;if(n.touchReleaseOnEdges&&(w=0),m>0?(S&&c&&s.allowThresholdMove&&s.currentTranslate>(n.centeredSlides?t.minTranslate()-t.slidesSizesGrid[t.activeIndex+1]-(n.slidesPerView!=="auto"&&t.slides.length-n.slidesPerView>=2?t.slidesSizesGrid[t.activeIndex+1]+t.params.spaceBetween:0)-t.params.spaceBetween:t.minTranslate())&&t.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>t.minTranslate()&&(u=!1,n.resistance&&(s.currentTranslate=t.minTranslate()-1+(-t.minTranslate()+s.startTranslate+m)**w))):m<0&&(S&&c&&s.allowThresholdMove&&s.currentTranslate<(n.centeredSlides?t.maxTranslate()+t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween+(n.slidesPerView!=="auto"&&t.slides.length-n.slidesPerView>=2?t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween:0):t.maxTranslate())&&t.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:t.slides.length-(n.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),s.currentTranslate<t.maxTranslate()&&(u=!1,n.resistance&&(s.currentTranslate=t.maxTranslate()+1-(t.maxTranslate()-s.startTranslate-m)**w))),u&&(d.preventedByNestedSwiper=!0),!t.allowSlideNext&&t.swipeDirection==="next"&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!t.allowSlidePrev&&t.swipeDirection==="prev"&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),!t.allowSlidePrev&&!t.allowSlideNext&&(s.currentTranslate=s.startTranslate),n.threshold>0)if(Math.abs(m)>n.threshold||s.allowThresholdMove){if(!s.allowThresholdMove){s.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,s.currentTranslate=s.startTranslate,r.diff=t.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY;return}}else{s.currentTranslate=s.startTranslate;return}!n.followFinger||n.cssMode||((n.freeMode&&n.freeMode.enabled&&t.freeMode||n.watchSlidesProgress)&&(t.updateActiveIndex(),t.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&t.freeMode&&t.freeMode.onTouchMove(),t.updateProgress(s.currentTranslate),t.setTranslate(s.currentTranslate))}function Yt(i){const e=this,t=e.touchEventsData;let s=i;s.originalEvent&&(s=s.originalEvent);let n;if(s.type==="touchend"||s.type==="touchcancel"){if(n=[...s.changedTouches].find(y=>y.identifier===t.touchId),!n||n.identifier!==t.touchId)return}else{if(t.touchId!==null||s.pointerId!==t.pointerId)return;n=s}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(s.type)&&!(["pointercancel","contextmenu"].includes(s.type)&&(e.browser.isSafari||e.browser.isWebView)))return;t.pointerId=null,t.touchId=null;const{params:l,touches:a,rtlTranslate:d,slidesGrid:o,enabled:f}=e;if(!f||!l.simulateTouch&&s.pointerType==="mouse")return;if(t.allowTouchCallbacks&&e.emit("touchEnd",s),t.allowTouchCallbacks=!1,!t.isTouched){t.isMoved&&l.grabCursor&&e.setGrabCursor(!1),t.isMoved=!1,t.startMoving=!1;return}l.grabCursor&&t.isMoved&&t.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const p=J(),h=p-t.touchStartTime;if(e.allowClick){const y=s.path||s.composedPath&&s.composedPath();e.updateClickedSlide(y&&y[0]||s.target,y),e.emit("tap click",s),h<300&&p-t.lastClickTime<300&&e.emit("doubleTap doubleClick",s)}if(t.lastClickTime=J(),xe(()=>{e.destroyed||(e.allowClick=!0)}),!t.isTouched||!t.isMoved||!e.swipeDirection||a.diff===0&&!t.loopSwapReset||t.currentTranslate===t.startTranslate&&!t.loopSwapReset){t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;return}t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;let g;if(l.followFinger?g=d?e.translate:-e.translate:g=-t.currentTranslate,l.cssMode)return;if(l.freeMode&&l.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:g});return}const m=g>=-e.maxTranslate()&&!e.params.loop;let v=0,b=e.slidesSizesGrid[0];for(let y=0;y<o.length;y+=y<l.slidesPerGroupSkip?1:l.slidesPerGroup){const I=y<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;typeof o[y+I]<"u"?(m||g>=o[y]&&g<o[y+I])&&(v=y,b=o[y+I]-o[y]):(m||g>=o[y])&&(v=y,b=o[o.length-1]-o[o.length-2])}let S=null,c=null;l.rewind&&(e.isBeginning?c=l.virtual&&l.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(S=0));const u=(g-o[v])/b,w=v<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;if(h>l.longSwipesMs){if(!l.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(u>=l.longSwipesRatio?e.slideTo(l.rewind&&e.isEnd?S:v+w):e.slideTo(v)),e.swipeDirection==="prev"&&(u>1-l.longSwipesRatio?e.slideTo(v+w):c!==null&&u<0&&Math.abs(u)>l.longSwipesRatio?e.slideTo(c):e.slideTo(v))}else{if(!l.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(s.target===e.navigation.nextEl||s.target===e.navigation.prevEl)?s.target===e.navigation.nextEl?e.slideTo(v+w):e.slideTo(v):(e.swipeDirection==="next"&&e.slideTo(S!==null?S:v+w),e.swipeDirection==="prev"&&e.slideTo(c!==null?c:v))}}function ze(){const i=this,{params:e,el:t}=i;if(t&&t.offsetWidth===0)return;e.breakpoints&&i.setBreakpoint();const{allowSlideNext:s,allowSlidePrev:n,snapGrid:r}=i,l=i.virtual&&i.params.virtual.enabled;i.allowSlideNext=!0,i.allowSlidePrev=!0,i.updateSize(),i.updateSlides(),i.updateSlidesClasses();const a=l&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&i.isEnd&&!i.isBeginning&&!i.params.centeredSlides&&!a?i.slideTo(i.slides.length-1,0,!1,!0):i.params.loop&&!l?i.slideToLoop(i.realIndex,0,!1,!0):i.slideTo(i.activeIndex,0,!1,!0),i.autoplay&&i.autoplay.running&&i.autoplay.paused&&(clearTimeout(i.autoplay.resizeTimeout),i.autoplay.resizeTimeout=setTimeout(()=>{i.autoplay&&i.autoplay.running&&i.autoplay.paused&&i.autoplay.resume()},500)),i.allowSlidePrev=n,i.allowSlideNext=s,i.params.watchOverflow&&r!==i.snapGrid&&i.checkOverflow()}function Ut(i){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&i.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(i.stopPropagation(),i.stopImmediatePropagation())))}function Kt(){const i=this,{wrapperEl:e,rtlTranslate:t,enabled:s}=i;if(!s)return;i.previousTranslate=i.translate,i.isHorizontal()?i.translate=-e.scrollLeft:i.translate=-e.scrollTop,i.translate===0&&(i.translate=0),i.updateActiveIndex(),i.updateSlidesClasses();let n;const r=i.maxTranslate()-i.minTranslate();r===0?n=0:n=(i.translate-i.minTranslate())/r,n!==i.progress&&i.updateProgress(t?-i.translate:i.translate),i.emit("setTranslate",i.translate,!1)}function Qt(i){const e=this;re(e,i.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}function Zt(){const i=this;i.documentTouchHandlerProceeded||(i.documentTouchHandlerProceeded=!0,i.params.touchReleaseOnEdges&&(i.el.style.touchAction="auto"))}const Ne=(i,e)=>{const t=F(),{params:s,el:n,wrapperEl:r,device:l}=i,a=!!s.nested,d=e==="on"?"addEventListener":"removeEventListener",o=e;!n||typeof n=="string"||(t[d]("touchstart",i.onDocumentTouchStart,{passive:!1,capture:a}),n[d]("touchstart",i.onTouchStart,{passive:!1}),n[d]("pointerdown",i.onTouchStart,{passive:!1}),t[d]("touchmove",i.onTouchMove,{passive:!1,capture:a}),t[d]("pointermove",i.onTouchMove,{passive:!1,capture:a}),t[d]("touchend",i.onTouchEnd,{passive:!0}),t[d]("pointerup",i.onTouchEnd,{passive:!0}),t[d]("pointercancel",i.onTouchEnd,{passive:!0}),t[d]("touchcancel",i.onTouchEnd,{passive:!0}),t[d]("pointerout",i.onTouchEnd,{passive:!0}),t[d]("pointerleave",i.onTouchEnd,{passive:!0}),t[d]("contextmenu",i.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&n[d]("click",i.onClick,!0),s.cssMode&&r[d]("scroll",i.onScroll),s.updateOnWindowResize?i[o](l.ios||l.android?"resize orientationchange observerUpdate":"resize observerUpdate",ze,!0):i[o]("observerUpdate",ze,!0),n[d]("load",i.onLoad,{capture:!0}))};function Jt(){const i=this,{params:e}=i;i.onTouchStart=Xt.bind(i),i.onTouchMove=jt.bind(i),i.onTouchEnd=Yt.bind(i),i.onDocumentTouchStart=Zt.bind(i),e.cssMode&&(i.onScroll=Kt.bind(i)),i.onClick=Ut.bind(i),i.onLoad=Qt.bind(i),Ne(i,"on")}function ei(){Ne(this,"off")}var ti={attachEvents:Jt,detachEvents:ei};const ke=(i,e)=>i.grid&&e.grid&&e.grid.rows>1;function ii(){const i=this,{realIndex:e,initialized:t,params:s,el:n}=i,r=s.breakpoints;if(!r||r&&Object.keys(r).length===0)return;const l=F(),a=s.breakpointsBase==="window"||!s.breakpointsBase?s.breakpointsBase:"container",d=["window","container"].includes(s.breakpointsBase)||!s.breakpointsBase?i.el:l.querySelector(s.breakpointsBase),o=i.getBreakpoint(r,a,d);if(!o||i.currentBreakpoint===o)return;const p=(o in r?r[o]:void 0)||i.originalParams,h=ke(i,s),g=ke(i,p),m=i.params.grabCursor,v=p.grabCursor,b=s.enabled;h&&!g?(n.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),i.emitContainerClasses()):!h&&g&&(n.classList.add(`${s.containerModifierClass}grid`),(p.grid.fill&&p.grid.fill==="column"||!p.grid.fill&&s.grid.fill==="column")&&n.classList.add(`${s.containerModifierClass}grid-column`),i.emitContainerClasses()),m&&!v?i.unsetGrabCursor():!m&&v&&i.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(I=>{if(typeof p[I]>"u")return;const x=s[I]&&s[I].enabled,C=p[I]&&p[I].enabled;x&&!C&&i[I].disable(),!x&&C&&i[I].enable()});const S=p.direction&&p.direction!==s.direction,c=s.loop&&(p.slidesPerView!==s.slidesPerView||S),u=s.loop;S&&t&&i.changeDirection(),$(i.params,p);const w=i.params.enabled,y=i.params.loop;Object.assign(i,{allowTouchMove:i.params.allowTouchMove,allowSlideNext:i.params.allowSlideNext,allowSlidePrev:i.params.allowSlidePrev}),b&&!w?i.disable():!b&&w&&i.enable(),i.currentBreakpoint=o,i.emit("_beforeBreakpoint",p),t&&(c?(i.loopDestroy(),i.loopCreate(e),i.updateSlides()):!u&&y?(i.loopCreate(e),i.updateSlides()):u&&!y&&i.loopDestroy()),i.emit("breakpoint",p)}function si(i,e,t){if(e===void 0&&(e="window"),!i||e==="container"&&!t)return;let s=!1;const n=R(),r=e==="window"?n.innerHeight:t.clientHeight,l=Object.keys(i).map(a=>{if(typeof a=="string"&&a.indexOf("@")===0){const d=parseFloat(a.substr(1));return{value:r*d,point:a}}return{value:a,point:a}});l.sort((a,d)=>parseInt(a.value,10)-parseInt(d.value,10));for(let a=0;a<l.length;a+=1){const{point:d,value:o}=l[a];e==="window"?n.matchMedia(`(min-width: ${o}px)`).matches&&(s=d):o<=t.clientWidth&&(s=d)}return s||"max"}var ri={setBreakpoint:ii,getBreakpoint:si};function ni(i,e){const t=[];return i.forEach(s=>{typeof s=="object"?Object.keys(s).forEach(n=>{s[n]&&t.push(e+n)}):typeof s=="string"&&t.push(e+s)}),t}function ai(){const i=this,{classNames:e,params:t,rtl:s,el:n,device:r}=i,l=ni(["initialized",t.direction,{"free-mode":i.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:s},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&t.grid.fill==="column"},{android:r.android},{ios:r.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...l),n.classList.add(...e),i.emitContainerClasses()}function li(){const i=this,{el:e,classNames:t}=i;!e||typeof e=="string"||(e.classList.remove(...t),i.emitContainerClasses())}var oi={addClasses:ai,removeClasses:li};function di(){const i=this,{isLocked:e,params:t}=i,{slidesOffsetBefore:s}=t;if(s){const n=i.slides.length-1,r=i.slidesGrid[n]+i.slidesSizesGrid[n]+s*2;i.isLocked=i.size>r}else i.isLocked=i.snapGrid.length===1;t.allowSlideNext===!0&&(i.allowSlideNext=!i.isLocked),t.allowSlidePrev===!0&&(i.allowSlidePrev=!i.isLocked),e&&e!==i.isLocked&&(i.isEnd=!1),e!==i.isLocked&&i.emit(i.isLocked?"lock":"unlock")}var ci={checkOverflow:di},Ge={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function fi(i,e){return function(s){s===void 0&&(s={});const n=Object.keys(s)[0],r=s[n];if(typeof r!="object"||r===null){$(e,s);return}if(i[n]===!0&&(i[n]={enabled:!0}),n==="navigation"&&i[n]&&i[n].enabled&&!i[n].prevEl&&!i[n].nextEl&&(i[n].auto=!0),["pagination","scrollbar"].indexOf(n)>=0&&i[n]&&i[n].enabled&&!i[n].el&&(i[n].auto=!0),!(n in i&&"enabled"in r)){$(e,s);return}typeof i[n]=="object"&&!("enabled"in i[n])&&(i[n].enabled=!0),i[n]||(i[n]={enabled:!1}),$(e,s)}}const ge={eventsEmitter:ot,update:St,translate:It,transition:Lt,slide:Gt,loop:Vt,grabCursor:Wt,events:ti,breakpoints:ri,checkOverflow:ci,classes:oi},ve={};class V{constructor(){let e,t;for(var s=arguments.length,n=new Array(s),r=0;r<s;r++)n[r]=arguments[r];n.length===1&&n[0].constructor&&Object.prototype.toString.call(n[0]).slice(8,-1)==="Object"?t=n[0]:[e,t]=n,t||(t={}),t=$({},t),e&&!t.el&&(t.el=e);const l=F();if(t.el&&typeof t.el=="string"&&l.querySelectorAll(t.el).length>1){const f=[];return l.querySelectorAll(t.el).forEach(p=>{const h=$({},t,{el:p});f.push(new V(h))}),f}const a=this;a.__swiper__=!0,a.support=Pe(),a.device=Le({userAgent:t.userAgent}),a.browser=Ae(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],t.modules&&Array.isArray(t.modules)&&a.modules.push(...t.modules);const d={};a.modules.forEach(f=>{f({params:t,swiper:a,extendParams:fi(t,d),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});const o=$({},Ge,d);return a.params=$({},o,ve,t),a.originalParams=$({},a.params),a.passedParams=$({},t),a.params&&a.params.on&&Object.keys(a.params.on).forEach(f=>{a.on(f,a.params.on[f])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return a.params.direction==="horizontal"},isVertical(){return a.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:s}=this,n=H(t,`.${s.slideClass}, swiper-slide`),r=se(n[0]);return se(e)-r}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>t.getAttribute("data-swiper-slide-index")*1===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&(this.params.grid.fill==="column"?e=Math.floor(e/this.params.grid.rows):this.params.grid.fill==="row"&&(e=e%Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){const e=this,{slidesEl:t,params:s}=e;e.slides=H(t,`.${s.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const s=this;e=Math.min(Math.max(e,0),1);const n=s.minTranslate(),l=(s.maxTranslate()-n)*e+n;s.translateTo(l,typeof t>"u"?0:t),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter(s=>s.indexOf("swiper")===0||s.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter(s=>s.indexOf("swiper-slide")===0||s.indexOf(t.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach(s=>{const n=e.getSlideClasses(s);t.push({slideEl:s,classNames:n}),e.emit("_slideClass",s,n)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){e===void 0&&(e="current"),t===void 0&&(t=!1);const s=this,{params:n,slides:r,slidesGrid:l,slidesSizesGrid:a,size:d,activeIndex:o}=s;let f=1;if(typeof n.slidesPerView=="number")return n.slidesPerView;if(n.centeredSlides){let p=r[o]?Math.ceil(r[o].swiperSlideSize):0,h;for(let g=o+1;g<r.length;g+=1)r[g]&&!h&&(p+=Math.ceil(r[g].swiperSlideSize),f+=1,p>d&&(h=!0));for(let g=o-1;g>=0;g-=1)r[g]&&!h&&(p+=r[g].swiperSlideSize,f+=1,p>d&&(h=!0))}else if(e==="current")for(let p=o+1;p<r.length;p+=1)(t?l[p]+a[p]-l[o]<d:l[p]-l[o]<d)&&(f+=1);else for(let p=o-1;p>=0;p-=1)l[o]-l[p]<d&&(f+=1);return f}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:s}=e;s.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(l=>{l.complete&&re(e,l)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function n(){const l=e.rtlTranslate?e.translate*-1:e.translate,a=Math.min(Math.max(l,e.maxTranslate()),e.minTranslate());e.setTranslate(a),e.updateActiveIndex(),e.updateSlidesClasses()}let r;if(s.freeMode&&s.freeMode.enabled&&!s.cssMode)n(),s.autoHeight&&e.updateAutoHeight();else{if((s.slidesPerView==="auto"||s.slidesPerView>1)&&e.isEnd&&!s.centeredSlides){const l=e.virtual&&s.virtual.enabled?e.virtual.slides:e.slides;r=e.slideTo(l.length-1,0,!1,!0)}else r=e.slideTo(e.activeIndex,0,!1,!0);r||n()}s.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){t===void 0&&(t=!0);const s=this,n=s.params.direction;return e||(e=n==="horizontal"?"vertical":"horizontal"),e===n||e!=="horizontal"&&e!=="vertical"||(s.el.classList.remove(`${s.params.containerModifierClass}${n}`),s.el.classList.add(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.forEach(r=>{e==="vertical"?r.style.width="":r.style.height=""}),s.emit("changeDirection"),t&&s.update()),s}changeLanguageDirection(e){const t=this;t.rtl&&e==="rtl"||!t.rtl&&e==="ltr"||(t.rtl=e==="rtl",t.rtlTranslate=t.params.direction==="horizontal"&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let s=e||t.params.el;if(typeof s=="string"&&(s=document.querySelector(s)),!s)return!1;s.swiper=t,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const n=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let l=s&&s.shadowRoot&&s.shadowRoot.querySelector?s.shadowRoot.querySelector(n()):H(s,n())[0];return!l&&t.params.createElements&&(l=Q("div",t.params.wrapperClass),s.append(l),H(s,`.${t.params.slideClass}`).forEach(a=>{l.append(a)})),Object.assign(t,{el:s,wrapperEl:l,slidesEl:t.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:l,hostEl:t.isElement?s.parentNode.host:s,mounted:!0,rtl:s.dir.toLowerCase()==="rtl"||W(s,"direction")==="rtl",rtlTranslate:t.params.direction==="horizontal"&&(s.dir.toLowerCase()==="rtl"||W(s,"direction")==="rtl"),wrongRTL:W(l,"display")==="-webkit-box"}),!0}init(e){const t=this;if(t.initialized||t.mount(e)===!1)return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();const n=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&n.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),n.forEach(r=>{r.complete?re(t,r):r.addEventListener("load",l=>{re(t,l.target)})}),he(t),t.initialized=!0,he(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){e===void 0&&(e=!0),t===void 0&&(t=!0);const s=this,{params:n,el:r,wrapperEl:l,slides:a}=s;return typeof s.params>"u"||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),n.loop&&s.loopDestroy(),t&&(s.removeClasses(),r&&typeof r!="string"&&r.removeAttribute("style"),l&&l.removeAttribute("style"),a&&a.length&&a.forEach(d=>{d.classList.remove(n.slideVisibleClass,n.slideFullyVisibleClass,n.slideActiveClass,n.slideNextClass,n.slidePrevClass),d.removeAttribute("style"),d.removeAttribute("data-swiper-slide-index")})),s.emit("destroy"),Object.keys(s.eventsListeners).forEach(d=>{s.off(d)}),e!==!1&&(s.el&&typeof s.el!="string"&&(s.el.swiper=null),Ye(s)),s.destroyed=!0),null}static extendDefaults(e){$(ve,e)}static get extendedDefaults(){return ve}static get defaults(){return Ge}static installModule(e){V.prototype.__modules__||(V.prototype.__modules__=[]);const t=V.prototype.__modules__;typeof e=="function"&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(t=>V.installModule(t)),V):(V.installModule(e),V)}}Object.keys(ge).forEach(i=>{Object.keys(ge[i]).forEach(e=>{V.prototype[e]=ge[i][e]})}),V.use([at,lt]);function Re(i,e,t,s){return i.params.createElements&&Object.keys(s).forEach(n=>{if(!t[n]&&t.auto===!0){let r=H(i.el,`.${s[n]}`)[0];r||(r=Q("div",s[n]),r.className=s[n],i.el.append(r)),t[n]=r,e[n]=r}}),t}function ui(i){let{swiper:e,extendParams:t,on:s,emit:n}=i;t({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null};function r(m){let v;return m&&typeof m=="string"&&e.isElement&&(v=e.el.querySelector(m)||e.hostEl.querySelector(m),v)?v:(m&&(typeof m=="string"&&(v=[...document.querySelectorAll(m)]),e.params.uniqueNavElements&&typeof m=="string"&&v&&v.length>1&&e.el.querySelectorAll(m).length===1?v=e.el.querySelector(m):v&&v.length===1&&(v=v[0])),m&&!v?m:v)}function l(m,v){const b=e.params.navigation;m=G(m),m.forEach(S=>{S&&(S.classList[v?"add":"remove"](...b.disabledClass.split(" ")),S.tagName==="BUTTON"&&(S.disabled=v),e.params.watchOverflow&&e.enabled&&S.classList[e.isLocked?"add":"remove"](b.lockClass))})}function a(){const{nextEl:m,prevEl:v}=e.navigation;if(e.params.loop){l(v,!1),l(m,!1);return}l(v,e.isBeginning&&!e.params.rewind),l(m,e.isEnd&&!e.params.rewind)}function d(m){m.preventDefault(),!(e.isBeginning&&!e.params.loop&&!e.params.rewind)&&(e.slidePrev(),n("navigationPrev"))}function o(m){m.preventDefault(),!(e.isEnd&&!e.params.loop&&!e.params.rewind)&&(e.slideNext(),n("navigationNext"))}function f(){const m=e.params.navigation;if(e.params.navigation=Re(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(m.nextEl||m.prevEl))return;let v=r(m.nextEl),b=r(m.prevEl);Object.assign(e.navigation,{nextEl:v,prevEl:b}),v=G(v),b=G(b);const S=(c,u)=>{c&&c.addEventListener("click",u==="next"?o:d),!e.enabled&&c&&c.classList.add(...m.lockClass.split(" "))};v.forEach(c=>S(c,"next")),b.forEach(c=>S(c,"prev"))}function p(){let{nextEl:m,prevEl:v}=e.navigation;m=G(m),v=G(v);const b=(S,c)=>{S.removeEventListener("click",c==="next"?o:d),S.classList.remove(...e.params.navigation.disabledClass.split(" "))};m.forEach(S=>b(S,"next")),v.forEach(S=>b(S,"prev"))}s("init",()=>{e.params.navigation.enabled===!1?g():(f(),a())}),s("toEdge fromEdge lock unlock",()=>{a()}),s("destroy",()=>{p()}),s("enable disable",()=>{let{nextEl:m,prevEl:v}=e.navigation;if(m=G(m),v=G(v),e.enabled){a();return}[...m,...v].filter(b=>!!b).forEach(b=>b.classList.add(e.params.navigation.lockClass))}),s("click",(m,v)=>{let{nextEl:b,prevEl:S}=e.navigation;b=G(b),S=G(S);const c=v.target;let u=S.includes(c)||b.includes(c);if(e.isElement&&!u){const w=v.path||v.composedPath&&v.composedPath();w&&(u=w.find(y=>b.includes(y)||S.includes(y)))}if(e.params.navigation.hideOnClick&&!u){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===c||e.pagination.el.contains(c)))return;let w;b.length?w=b[0].classList.contains(e.params.navigation.hiddenClass):S.length&&(w=S[0].classList.contains(e.params.navigation.hiddenClass)),n(w===!0?"navigationShow":"navigationHide"),[...b,...S].filter(y=>!!y).forEach(y=>y.classList.toggle(e.params.navigation.hiddenClass))}});const h=()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),f(),a()},g=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),p()};Object.assign(e.navigation,{enable:h,disable:g,update:a,init:f,destroy:p})}function Z(i){return i===void 0&&(i=""),`.${i.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function pi(i){let{swiper:e,extendParams:t,on:s,emit:n}=i;const r="swiper-pagination";t({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:c=>c,formatFractionTotal:c=>c,bulletClass:`${r}-bullet`,bulletActiveClass:`${r}-bullet-active`,modifierClass:`${r}-`,currentClass:`${r}-current`,totalClass:`${r}-total`,hiddenClass:`${r}-hidden`,progressbarFillClass:`${r}-progressbar-fill`,progressbarOppositeClass:`${r}-progressbar-opposite`,clickableClass:`${r}-clickable`,lockClass:`${r}-lock`,horizontalClass:`${r}-horizontal`,verticalClass:`${r}-vertical`,paginationDisabledClass:`${r}-disabled`}}),e.pagination={el:null,bullets:[]};let l,a=0;function d(){return!e.params.pagination.el||!e.pagination.el||Array.isArray(e.pagination.el)&&e.pagination.el.length===0}function o(c,u){const{bulletActiveClass:w}=e.params.pagination;c&&(c=c[`${u==="prev"?"previous":"next"}ElementSibling`],c&&(c.classList.add(`${w}-${u}`),c=c[`${u==="prev"?"previous":"next"}ElementSibling`],c&&c.classList.add(`${w}-${u}-${u}`)))}function f(c,u,w){if(c=c%w,u=u%w,u===c+1)return"next";if(u===c-1)return"previous"}function p(c){const u=c.target.closest(Z(e.params.pagination.bulletClass));if(!u)return;c.preventDefault();const w=se(u)*e.params.slidesPerGroup;if(e.params.loop){if(e.realIndex===w)return;const y=f(e.realIndex,w,e.slides.length);y==="next"?e.slideNext():y==="previous"?e.slidePrev():e.slideToLoop(w)}else e.slideTo(w)}function h(){const c=e.rtl,u=e.params.pagination;if(d())return;let w=e.pagination.el;w=G(w);let y,I;const x=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,C=e.params.loop?Math.ceil(x/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(I=e.previousRealIndex||0,y=e.params.slidesPerGroup>1?Math.floor(e.realIndex/e.params.slidesPerGroup):e.realIndex):typeof e.snapIndex<"u"?(y=e.snapIndex,I=e.previousSnapIndex):(I=e.previousIndex||0,y=e.activeIndex||0),u.type==="bullets"&&e.pagination.bullets&&e.pagination.bullets.length>0){const P=e.pagination.bullets;let T,E,L;if(u.dynamicBullets&&(l=de(P[0],e.isHorizontal()?"width":"height"),w.forEach(O=>{O.style[e.isHorizontal()?"width":"height"]=`${l*(u.dynamicMainBullets+4)}px`}),u.dynamicMainBullets>1&&I!==void 0&&(a+=y-(I||0),a>u.dynamicMainBullets-1?a=u.dynamicMainBullets-1:a<0&&(a=0)),T=Math.max(y-a,0),E=T+(Math.min(P.length,u.dynamicMainBullets)-1),L=(E+T)/2),P.forEach(O=>{const D=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(_=>`${u.bulletActiveClass}${_}`)].map(_=>typeof _=="string"&&_.includes(" ")?_.split(" "):_).flat();O.classList.remove(...D)}),w.length>1)P.forEach(O=>{const D=se(O);D===y?O.classList.add(...u.bulletActiveClass.split(" ")):e.isElement&&O.setAttribute("part","bullet"),u.dynamicBullets&&(D>=T&&D<=E&&O.classList.add(...`${u.bulletActiveClass}-main`.split(" ")),D===T&&o(O,"prev"),D===E&&o(O,"next"))});else{const O=P[y];if(O&&O.classList.add(...u.bulletActiveClass.split(" ")),e.isElement&&P.forEach((D,_)=>{D.setAttribute("part",_===y?"bullet-active":"bullet")}),u.dynamicBullets){const D=P[T],_=P[E];for(let A=T;A<=E;A+=1)P[A]&&P[A].classList.add(...`${u.bulletActiveClass}-main`.split(" "));o(D,"prev"),o(_,"next")}}if(u.dynamicBullets){const O=Math.min(P.length,u.dynamicMainBullets+4),D=(l*O-l)/2-L*l,_=c?"right":"left";P.forEach(A=>{A.style[e.isHorizontal()?_:"top"]=`${D}px`})}}w.forEach((P,T)=>{if(u.type==="fraction"&&(P.querySelectorAll(Z(u.currentClass)).forEach(E=>{E.textContent=u.formatFractionCurrent(y+1)}),P.querySelectorAll(Z(u.totalClass)).forEach(E=>{E.textContent=u.formatFractionTotal(C)})),u.type==="progressbar"){let E;u.progressbarOpposite?E=e.isHorizontal()?"vertical":"horizontal":E=e.isHorizontal()?"horizontal":"vertical";const L=(y+1)/C;let O=1,D=1;E==="horizontal"?O=L:D=L,P.querySelectorAll(Z(u.progressbarFillClass)).forEach(_=>{_.style.transform=`translate3d(0,0,0) scaleX(${O}) scaleY(${D})`,_.style.transitionDuration=`${e.params.speed}ms`})}u.type==="custom"&&u.renderCustom?(Me(P,u.renderCustom(e,y+1,C)),T===0&&n("paginationRender",P)):(T===0&&n("paginationRender",P),n("paginationUpdate",P)),e.params.watchOverflow&&e.enabled&&P.classList[e.isLocked?"add":"remove"](u.lockClass)})}function g(){const c=e.params.pagination;if(d())return;const u=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.grid&&e.params.grid.rows>1?e.slides.length/Math.ceil(e.params.grid.rows):e.slides.length;let w=e.pagination.el;w=G(w);let y="";if(c.type==="bullets"){let I=e.params.loop?Math.ceil(u/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&e.params.freeMode.enabled&&I>u&&(I=u);for(let x=0;x<I;x+=1)c.renderBullet?y+=c.renderBullet.call(e,x,c.bulletClass):y+=`<${c.bulletElement} ${e.isElement?'part="bullet"':""} class="${c.bulletClass}"></${c.bulletElement}>`}c.type==="fraction"&&(c.renderFraction?y=c.renderFraction.call(e,c.currentClass,c.totalClass):y=`<span class="${c.currentClass}"></span> / <span class="${c.totalClass}"></span>`),c.type==="progressbar"&&(c.renderProgressbar?y=c.renderProgressbar.call(e,c.progressbarFillClass):y=`<span class="${c.progressbarFillClass}"></span>`),e.pagination.bullets=[],w.forEach(I=>{c.type!=="custom"&&Me(I,y||""),c.type==="bullets"&&e.pagination.bullets.push(...I.querySelectorAll(Z(c.bulletClass)))}),c.type!=="custom"&&n("paginationRender",w[0])}function m(){e.params.pagination=Re(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});const c=e.params.pagination;if(!c.el)return;let u;typeof c.el=="string"&&e.isElement&&(u=e.el.querySelector(c.el)),!u&&typeof c.el=="string"&&(u=[...document.querySelectorAll(c.el)]),u||(u=c.el),!(!u||u.length===0)&&(e.params.uniqueNavElements&&typeof c.el=="string"&&Array.isArray(u)&&u.length>1&&(u=[...e.el.querySelectorAll(c.el)],u.length>1&&(u=u.find(w=>Ce(w,".swiper")[0]===e.el))),Array.isArray(u)&&u.length===1&&(u=u[0]),Object.assign(e.pagination,{el:u}),u=G(u),u.forEach(w=>{c.type==="bullets"&&c.clickable&&w.classList.add(...(c.clickableClass||"").split(" ")),w.classList.add(c.modifierClass+c.type),w.classList.add(e.isHorizontal()?c.horizontalClass:c.verticalClass),c.type==="bullets"&&c.dynamicBullets&&(w.classList.add(`${c.modifierClass}${c.type}-dynamic`),a=0,c.dynamicMainBullets<1&&(c.dynamicMainBullets=1)),c.type==="progressbar"&&c.progressbarOpposite&&w.classList.add(c.progressbarOppositeClass),c.clickable&&w.addEventListener("click",p),e.enabled||w.classList.add(c.lockClass)}))}function v(){const c=e.params.pagination;if(d())return;let u=e.pagination.el;u&&(u=G(u),u.forEach(w=>{w.classList.remove(c.hiddenClass),w.classList.remove(c.modifierClass+c.type),w.classList.remove(e.isHorizontal()?c.horizontalClass:c.verticalClass),c.clickable&&(w.classList.remove(...(c.clickableClass||"").split(" ")),w.removeEventListener("click",p))})),e.pagination.bullets&&e.pagination.bullets.forEach(w=>w.classList.remove(...c.bulletActiveClass.split(" ")))}s("changeDirection",()=>{if(!e.pagination||!e.pagination.el)return;const c=e.params.pagination;let{el:u}=e.pagination;u=G(u),u.forEach(w=>{w.classList.remove(c.horizontalClass,c.verticalClass),w.classList.add(e.isHorizontal()?c.horizontalClass:c.verticalClass)})}),s("init",()=>{e.params.pagination.enabled===!1?S():(m(),g(),h())}),s("activeIndexChange",()=>{typeof e.snapIndex>"u"&&h()}),s("snapIndexChange",()=>{h()}),s("snapGridLengthChange",()=>{g(),h()}),s("destroy",()=>{v()}),s("enable disable",()=>{let{el:c}=e.pagination;c&&(c=G(c),c.forEach(u=>u.classList[e.enabled?"remove":"add"](e.params.pagination.lockClass)))}),s("lock unlock",()=>{h()}),s("click",(c,u)=>{const w=u.target,y=G(e.pagination.el);if(e.params.pagination.el&&e.params.pagination.hideOnClick&&y&&y.length>0&&!w.classList.contains(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&w===e.navigation.nextEl||e.navigation.prevEl&&w===e.navigation.prevEl))return;const I=y[0].classList.contains(e.params.pagination.hiddenClass);n(I===!0?"paginationShow":"paginationHide"),y.forEach(x=>x.classList.toggle(e.params.pagination.hiddenClass))}});const b=()=>{e.el.classList.remove(e.params.pagination.paginationDisabledClass);let{el:c}=e.pagination;c&&(c=G(c),c.forEach(u=>u.classList.remove(e.params.pagination.paginationDisabledClass))),m(),g(),h()},S=()=>{e.el.classList.add(e.params.pagination.paginationDisabledClass);let{el:c}=e.pagination;c&&(c=G(c),c.forEach(u=>u.classList.add(e.params.pagination.paginationDisabledClass))),v()};Object.assign(e.pagination,{enable:b,disable:S,render:g,update:h,init:m,destroy:v})}function mi(i){let{swiper:e,extendParams:t,on:s,emit:n,params:r}=i;e.autoplay={running:!1,paused:!1,timeLeft:0},t({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let l,a,d=r&&r.autoplay?r.autoplay.delay:3e3,o=r&&r.autoplay?r.autoplay.delay:3e3,f,p=new Date().getTime(),h,g,m,v,b,S,c;function u(M){!e||e.destroyed||!e.wrapperEl||M.target===e.wrapperEl&&(e.wrapperEl.removeEventListener("transitionend",u),!(c||M.detail&&M.detail.bySwiperTouchMove)&&T())}const w=()=>{if(e.destroyed||!e.autoplay.running)return;e.autoplay.paused?h=!0:h&&(o=f,h=!1);const M=e.autoplay.paused?f:p+o-new Date().getTime();e.autoplay.timeLeft=M,n("autoplayTimeLeft",M,M/d),a=requestAnimationFrame(()=>{w()})},y=()=>{let M;return e.virtual&&e.params.virtual.enabled?M=e.slides.find(B=>B.classList.contains("swiper-slide-active")):M=e.slides[e.activeIndex],M?parseInt(M.getAttribute("data-swiper-autoplay"),10):void 0},I=M=>{if(e.destroyed||!e.autoplay.running)return;cancelAnimationFrame(a),w();let k=typeof M>"u"?e.params.autoplay.delay:M;d=e.params.autoplay.delay,o=e.params.autoplay.delay;const B=y();!Number.isNaN(B)&&B>0&&typeof M>"u"&&(k=B,d=B,o=B),f=k;const j=e.params.speed,ae=()=>{!e||e.destroyed||(e.params.autoplay.reverseDirection?!e.isBeginning||e.params.loop||e.params.rewind?(e.slidePrev(j,!0,!0),n("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(e.slides.length-1,j,!0,!0),n("autoplay")):!e.isEnd||e.params.loop||e.params.rewind?(e.slideNext(j,!0,!0),n("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(0,j,!0,!0),n("autoplay")),e.params.cssMode&&(p=new Date().getTime(),requestAnimationFrame(()=>{I()})))};return k>0?(clearTimeout(l),l=setTimeout(()=>{ae()},k)):requestAnimationFrame(()=>{ae()}),k},x=()=>{p=new Date().getTime(),e.autoplay.running=!0,I(),n("autoplayStart")},C=()=>{e.autoplay.running=!1,clearTimeout(l),cancelAnimationFrame(a),n("autoplayStop")},P=(M,k)=>{if(e.destroyed||!e.autoplay.running)return;clearTimeout(l),M||(S=!0);const B=()=>{n("autoplayPause"),e.params.autoplay.waitForTransition?e.wrapperEl.addEventListener("transitionend",u):T()};if(e.autoplay.paused=!0,k){b&&(f=e.params.autoplay.delay),b=!1,B();return}f=(f||e.params.autoplay.delay)-(new Date().getTime()-p),!(e.isEnd&&f<0&&!e.params.loop)&&(f<0&&(f=0),B())},T=()=>{e.isEnd&&f<0&&!e.params.loop||e.destroyed||!e.autoplay.running||(p=new Date().getTime(),S?(S=!1,I(f)):I(),e.autoplay.paused=!1,n("autoplayResume"))},E=()=>{if(e.destroyed||!e.autoplay.running)return;const M=F();M.visibilityState==="hidden"&&(S=!0,P(!0)),M.visibilityState==="visible"&&T()},L=M=>{M.pointerType==="mouse"&&(S=!0,c=!0,!(e.animating||e.autoplay.paused)&&P(!0))},O=M=>{M.pointerType==="mouse"&&(c=!1,e.autoplay.paused&&T())},D=()=>{e.params.autoplay.pauseOnMouseEnter&&(e.el.addEventListener("pointerenter",L),e.el.addEventListener("pointerleave",O))},_=()=>{e.el&&typeof e.el!="string"&&(e.el.removeEventListener("pointerenter",L),e.el.removeEventListener("pointerleave",O))},A=()=>{F().addEventListener("visibilitychange",E)},N=()=>{F().removeEventListener("visibilitychange",E)};s("init",()=>{e.params.autoplay.enabled&&(D(),A(),x())}),s("destroy",()=>{_(),N(),e.autoplay.running&&C()}),s("_freeModeStaticRelease",()=>{(m||S)&&T()}),s("_freeModeNoMomentumRelease",()=>{e.params.autoplay.disableOnInteraction?C():P(!0,!0)}),s("beforeTransitionStart",(M,k,B)=>{e.destroyed||!e.autoplay.running||(B||!e.params.autoplay.disableOnInteraction?P(!0,!0):C())}),s("sliderFirstMove",()=>{if(!(e.destroyed||!e.autoplay.running)){if(e.params.autoplay.disableOnInteraction){C();return}g=!0,m=!1,S=!1,v=setTimeout(()=>{S=!0,m=!0,P(!0)},200)}}),s("touchEnd",()=>{if(!(e.destroyed||!e.autoplay.running||!g)){if(clearTimeout(v),clearTimeout(l),e.params.autoplay.disableOnInteraction){m=!1,g=!1;return}m&&e.params.cssMode&&T(),m=!1,g=!1}}),s("slideChange",()=>{e.destroyed||!e.autoplay.running||(b=!0)}),Object.assign(e.autoplay,{start:x,stop:C,pause:P,resume:T})}function hi(i){const{effect:e,swiper:t,on:s,setTranslate:n,setTransition:r,overwriteParams:l,perspective:a,recreateShadows:d,getEffectParams:o}=i;s("beforeInit",()=>{if(t.params.effect!==e)return;t.classNames.push(`${t.params.containerModifierClass}${e}`),a&&a()&&t.classNames.push(`${t.params.containerModifierClass}3d`);const p=l?l():{};Object.assign(t.params,p),Object.assign(t.originalParams,p)}),s("setTranslate _virtualUpdated",()=>{t.params.effect===e&&n()}),s("setTransition",(p,h)=>{t.params.effect===e&&r(h)}),s("transitionEnd",()=>{if(t.params.effect===e&&d){if(!o||!o().slideShadows)return;t.slides.forEach(p=>{p.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(h=>h.remove())}),d()}});let f;s("virtualUpdate",()=>{t.params.effect===e&&(t.slides.length||(f=!0),requestAnimationFrame(()=>{f&&t.slides&&t.slides.length&&(n(),f=!1)}))})}function gi(i,e){const t=oe(e);return t!==e&&(t.style.backfaceVisibility="hidden",t.style["-webkit-backface-visibility"]="hidden"),t}function Be(i,e,t){const s=`swiper-slide-shadow${t?`-${t}`:""}${` swiper-slide-shadow-${i}`}`,n=oe(e);let r=n.querySelector(`.${s.split(" ").join(".")}`);return r||(r=Q("div",s.split(" ")),n.append(r)),r}function vi(i){let{swiper:e,extendParams:t,on:s}=i;t({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}}),hi({effect:"coverflow",swiper:e,on:s,setTranslate:()=>{const{width:l,height:a,slides:d,slidesSizesGrid:o}=e,f=e.params.coverflowEffect,p=e.isHorizontal(),h=e.translate,g=p?-h+l/2:-h+a/2,m=p?f.rotate:-f.rotate,v=f.depth,b=it(e);for(let S=0,c=d.length;S<c;S+=1){const u=d[S],w=o[S],y=u.swiperSlideOffset,I=(g-y-w/2)/w,x=typeof f.modifier=="function"?f.modifier(I):I*f.modifier;let C=p?m*x:0,P=p?0:m*x,T=-v*Math.abs(x),E=f.stretch;typeof E=="string"&&E.indexOf("%")!==-1&&(E=parseFloat(f.stretch)/100*w);let L=p?0:E*x,O=p?E*x:0,D=1-(1-f.scale)*Math.abs(x);Math.abs(O)<.001&&(O=0),Math.abs(L)<.001&&(L=0),Math.abs(T)<.001&&(T=0),Math.abs(C)<.001&&(C=0),Math.abs(P)<.001&&(P=0),Math.abs(D)<.001&&(D=0);const _=`translate3d(${O}px,${L}px,${T}px)  rotateX(${b(P)}deg) rotateY(${b(C)}deg) scale(${D})`,A=gi(f,u);if(A.style.transform=_,u.style.zIndex=-Math.abs(Math.round(x))+1,f.slideShadows){let N=p?u.querySelector(".swiper-slide-shadow-left"):u.querySelector(".swiper-slide-shadow-top"),M=p?u.querySelector(".swiper-slide-shadow-right"):u.querySelector(".swiper-slide-shadow-bottom");N||(N=Be("coverflow",u,p?"left":"top")),M||(M=Be("coverflow",u,p?"right":"bottom")),N&&(N.style.opacity=x>0?x:0),M&&(M.style.opacity=-x>0?-x:0)}}},setTransition:l=>{e.slides.map(d=>oe(d)).forEach(d=>{d.style.transitionDuration=`${l}ms`,d.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(o=>{o.style.transitionDuration=`${l}ms`})})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})})}const $e=i=>{try{return document.querySelector(i)}catch{return null}},wi=i=>{try{return document.querySelectorAll(i)}catch{return document.querySelectorAll("")}},X=(i,e={},t="")=>{try{const s=document.createElement(i);for(const[n,r]of Object.entries(e))n==="className"?s.className=String(r):n==="id"?s.id=String(r):s.setAttribute(n,String(r));return t&&(s.innerHTML=t),s}catch{return document.createElement("div")}},Y=(i,e)=>{try{i.appendChild(e)}catch{}},Si=(i,e)=>{try{i.prepend(e)}catch{}},we=i=>{try{i.remove()}catch{}},yi=(i,e)=>{try{for(const[t,s]of Object.entries(e))i.style.setProperty(t,String(s))}catch{}},Ve={USER_AGENT_SUBSTR_START:0,USER_AGENT_SUBSTR_LENGTH:4},Ti={MAX_ERROR_LOG_ENTRIES:50,DOM_READY_TIMEOUT:5e3,SLIDE_NUMBER_MIN:1,SLIDE_NUMBER_MAX:30,TRANSITION_TIME_MIN:1e3,TRANSITION_TIME_MAX:3e4,CONFIG_MAX_SLIDES_MIN:1,CONFIG_MAX_SLIDES_MAX:50},Se={SCREEN_WIDTH_MULTIPLIER:2,SCREEN_WIDTH_OFFSET:50,CONTAINER_MARGIN_MULTIPLIER:.254},Fe={SLIDE_INCREMENT:1,INITIAL_SLIDE_INDEX:1},ye={CHECK_INTERVAL:10,DATA_CHECK_INTERVAL:100,DEFAULT_TRANSITION_TIME:5e3},He={SWIPER_AD_CONTAINER_ID:"swiperAdContainer",HEADER_ICON_ID:"wusong8899HeaderAdvIcon"},Ei={AD_SWIPER:"adSwiper"},Te={SWIPER_PAGINATION_EL:".swiper-pagination",SWIPER_BUTTON_NEXT_EL:".swiper-button-next",SWIPER_BUTTON_PREV_EL:".swiper-button-prev"},ne={ID:"wusong8899-flarum-header-advertisement",TRANSLATION_PREFIX:"wusong8899-header-adv",MAX_SLIDES:30,HEADER_ICON_URL:"https://ex.cc/assets/files/date/test.png"},bi=()=>{try{return navigator.userAgent.substring(Ve.USER_AGENT_SUBSTR_START,Ve.USER_AGENT_SUBSTR_LENGTH)==="Mobi"}catch{return!1}},z={env:"production",app:{extensionId:ne.ID,translationPrefix:ne.TRANSLATION_PREFIX},slider:{maxSlides:ne.MAX_SLIDES,defaultTransitionTime:ye.DEFAULT_TRANSITION_TIME,checkTime:ye.CHECK_INTERVAL,dataCheckInterval:ye.DATA_CHECK_INTERVAL,dom:{containerId:He.SWIPER_AD_CONTAINER_ID,swiperClass:Ei.AD_SWIPER},swiper:{spaceBetween:30,effect:"coverflow",centeredSlides:!0,slidesPerView:2,coverflowEffect:{rotate:0,depth:100,modifier:1,slideShadows:!0,stretch:0},pagination:{el:Te.SWIPER_PAGINATION_EL,type:"bullets"},navigation:{nextEl:Te.SWIPER_BUTTON_NEXT_EL,prevEl:Te.SWIPER_BUTTON_PREV_EL}}},ui:{headerIconId:He.HEADER_ICON_ID,headerIconUrl:ne.HEADER_ICON_URL}};class xi{constructor(){this.maxSlides=z.slider.maxSlides,this.checkTime=z.slider.checkTime}getForumAttribute(e){try{const t=q&&q.forum,s=t&&t.attribute;return typeof s=="function"?s.call(t,e):void 0}catch{return}}attachAdvertiseHeader(e){try{this.destroy();const t=this.createContainer(),s=this.createSwiperElement(t),n=this.createSwiperWrapper(s);this.populateSlides(n),this.createPagination(s),this.createNavigation(s),this.container=t,this.appendToDOM(t),setTimeout(()=>{this.initializeSwiper(this.getTransitionTime())},this.checkTime)}catch{}}removeExistingNavigation(){const e=$e(`#${z.slider.dom.containerId}`);e&&we(e);const t=wi(".item-nav");for(const s of t)we(s)}createContainer(){this.removeExistingNavigation();const e=X("div",{id:z.slider.dom.containerId,className:"adContainer"});return this.applyMobileStyles(e),e}applyMobileStyles(e){if(bi()){const s=globalThis.innerWidth*Se.SCREEN_WIDTH_MULTIPLIER-Se.SCREEN_WIDTH_OFFSET;yi(e,{width:`${s}px`,"margin-left":`${-(s*Se.CONTAINER_MARGIN_MULTIPLIER)}px`})}}createSwiperElement(e){const t=X("div",{className:`swiper ${z.slider.dom.swiperClass}`});return Y(e,t),t}createSwiperWrapper(e){const t=X("div",{className:"swiper-wrapper"});return Y(e,t),t}getTransitionTime(){const e=this.getForumAttribute("wusong8899-flarum-header-advertisement.TransitionTime");return e?Number.parseInt(String(e),10):z.slider.defaultTransitionTime}populateSlides(e){for(let t=Fe.INITIAL_SLIDE_INDEX;t<=this.maxSlides;t+=Fe.SLIDE_INCREMENT){const s=this.getForumAttribute(`wusong8899-flarum-header-advertisement.Image${t}`),n=this.getForumAttribute(`wusong8899-flarum-header-advertisement.Link${t}`);if(s){const r=this.createSlide(String(s),String(n||""));Y(e,r)}}}createSlide(e,t){const s=X("div",{className:"swiper-slide"});let n="";return t&&(n=`window.location.href="${t}"`),s.innerHTML=`<img onclick='${n}' src='${e}' />`,s}createPagination(e){const t=X("div",{className:"swiper-pagination"});Y(e,t)}createNavigation(e){const t=X("div",{className:"swiper-button-prev"}),s=X("div",{className:"swiper-button-next"});Y(e,t),Y(e,s)}appendToDOM(e){const t=$e("#content .container");t&&Si(t,e)}initializeSwiper(e){try{this.swiper=new V(`.${z.slider.dom.swiperClass}`,{autoplay:{delay:e},spaceBetween:z.slider.swiper.spaceBetween,effect:z.slider.swiper.effect,centeredSlides:z.slider.swiper.centeredSlides,slidesPerView:z.slider.swiper.slidesPerView,coverflowEffect:{rotate:z.slider.swiper.coverflowEffect.rotate,depth:z.slider.swiper.coverflowEffect.depth,modifier:z.slider.swiper.coverflowEffect.modifier,slideShadows:z.slider.swiper.coverflowEffect.slideShadows,stretch:z.slider.swiper.coverflowEffect.stretch},pagination:{el:z.slider.swiper.pagination.el,type:z.slider.swiper.pagination.type},navigation:{nextEl:z.slider.swiper.navigation.nextEl,prevEl:z.slider.swiper.navigation.prevEl},modules:[vi,ui,pi,mi]})}catch{}}destroy(){this.swiper&&(this.swiper.destroy(!0,!0),delete this.swiper),this.container&&(we(this.container),delete this.container)}}class U{constructor(){this.errorLog=[],this.isInitialized=!1}static getInstance(){return U.instance||(U.instance=new U),U.instance}initialize(){try{return this.isInitialized||(this.setupGlobalErrorHandling(),this.isInitialized=!0),!0}catch{return!1}}handleSync(e,t){try{return e()}catch(s){this.logError(s,t);return}}async handleAsync(e,t){try{return await e()}catch(s){this.logError(s,t);return}}logError(e,t){try{const s={timestamp:new Date,error:e,context:t};this.errorLog.push(s),this.errorLog.length>Ti.MAX_ERROR_LOG_ENTRIES&&this.errorLog.shift()}catch{}}setupGlobalErrorHandling(){try{window.addEventListener("unhandledrejection",e=>{this.logError(new Error(String(e.reason)),"Unhandled Promise Rejection")})}catch{}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}}class K{constructor(){}static getInstance(){return K.instance||(K.instance=new K),K.instance}isTagsPage(){try{return q.current.get("routeName")==="tags"}catch{try{return window.location.pathname.includes("/tags")}catch{return!1}}}getConfig(){return z}isSlideshowConfigured(){try{for(let e=1;e<=z.slider.maxSlides;e++)if(q.forum.attribute(`FlarumHeaderAdvImage${e}`))return!0;return!1}catch{return!1}}}q.initializers.add(z.app.extensionId,()=>{const i=U.getInstance(),e=K.getInstance();if(!i.initialize())return;const t=new xi;We.extend(qe.prototype,"view",function(n){i.handleSync(()=>{e.isTagsPage()&&Ii(n,t)},"HeaderPrimary view extension")})});const Ii=(i,e)=>{try{try{e.attachAdvertiseHeader(i)}catch{}q.session.user||Ci()}catch{}},Ci=()=>{let i=document.getElementById(z.ui.headerIconId);if(i===null){const e=q.forum.attribute("wusong8899-flarum-header-advertisement.HeaderIconUrl")||z.ui.headerIconUrl;i=document.createElement("div"),i.id=z.ui.headerIconId,i.style.display="inline-block",i.style.marginTop="8px",i.innerHTML=`<img src="${e}" style="height: 24px;" />`;const t=document.querySelector("#app-navigation .App-backControl");t&&t.firstChild&&t.firstChild.before(i)}}})(flarum.core.compat["common/extend"],flarum.core.compat["forum/app"],flarum.core.compat["forum/components/HeaderPrimary"]);
//# sourceMappingURL=forum.js.map

module.exports={};