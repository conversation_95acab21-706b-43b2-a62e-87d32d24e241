import { MOBILE_DETECTION } from '../../common/config/constants';

/**
 * Mobile detection utility functions
 */

/**
 * Check if the current device is mobile
 */
export const isMobileDevice = (): boolean => {
    try {
        const userAgent = navigator.userAgent;
        const mobileIndicator = userAgent.substring(
            MOBILE_DETECTION.USER_AGENT_SUBSTR_START,
            MOBILE_DETECTION.USER_AGENT_SUBSTR_LENGTH
        );
        return mobileIndicator === 'Mobi';
    } catch {
        return false;
    }
};
