import app from 'flarum/forum/app';
import { defaultConfig } from '../../common/config';

/**
 * Configuration manager for the Header Advertisement extension
 */
export class ConfigManager {
    private static instance: ConfigManager;

    private constructor() { }

    /**
     * Get singleton instance
     */
    public static getInstance(): ConfigManager {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }

    /**
     * Check if current page is tags page
     */
    public isTagsPage(): boolean {
        try {
            const currentRoute = app.current.get('routeName');
            return currentRoute === 'tags';
        } catch {
            // Fallback: check URL
            try {
                return window.location.pathname.includes('/tags');
            } catch {
                return false;
            }
        }
    }

    /**
     * Get extension configuration
     */
    public getConfig(): typeof defaultConfig {
        return defaultConfig;
    }

    /**
     * Check if slideshow is properly configured
     */
    public isSlideshowConfigured(): boolean {
        try {
            // Check if at least one slide is configured
            for (let i = 1; i <= defaultConfig.slider.maxSlides; i++) {
                const image = app.forum.attribute(`FlarumHeaderAdvImage${i}`);
                if (image) {
                    return true;
                }
            }
            return false;
        } catch {
            return false;
        }
    }
}
